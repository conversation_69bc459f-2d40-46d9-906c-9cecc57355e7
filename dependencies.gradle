buildscript {
    ext.Versions = ['compileSdkVersion'   : 36,
                    'buildToolsVersion'   : "36.0.0",
                    'defaultMinSdkVersion': 26,
                    'targetSdkVersion'    : 35,
    ]

    ext.Deps = ['location'                : ":location",
                'bluetooth'               : ":bluetooth",
                'core'                    : ":STTAndroidCore",
                'composeUi'               : ":composeui",
                'utils'                   : ":utils",
                'timeline'                : ":timeline",
                'graphlib'                : ":graphlib",
                'multimedia'              : ":multimedia",
                'infoModel'               : ":infoModel",
                'analytics'               : ":analytics",
                'remote'                  : ":remote",
                'datasource'              : ":datasource",
                'persistence'             : ":persistence",
                'domain'                  : ":domain",
                'wear'                    : ":STTAndroidWear",
                'connectivity'            : ":SuuntoConnectivity",
                'mds'                     : ":MdsLibrary",
                'maps'                    : ":maps",
                'mapsProviderMapbox'      : ":mapsProviderMapbox",
                'mapsProviderGoogle'      : ":mapsProviderGoogle",
                'mapsProviderAmap'        : ":mapsProviderAmap",
                'configDebug'             : ":configdebug",
                'configRelease'           : ":configrelease",
                'remoteBase'              : ":remotebase",
                'workoutsRemote'          : ":workoutsremote",
                'workoutsDomain'          : ":workoutsdomain",
                'workoutsDataSource'      : ":workoutsdatasource",
                'userRemote'              : ":userremote",
                'domainBase'              : ":domainbase",
                'datasourceBase'          : ":datasourcebase",
                'userDomain'              : ":userdomain",
                'userDataSource'          : ":userdatasource",
                'testUtils'               : ":testutils",
                'analyticsRemote'         : ":analyticsremote",
                'sessionRemote'           : ":sessionremote",
                'sessionDataSource'       : ":sessiondatasource",
                'sessionDomain'           : ":sessiondomain",
                'appBase'                 : ":appbase",
                'explore'                 : ":explore",
                'exploreDomain'           : ":exploredomain",
                'exploreDatasource'       : ":exploredatasource",
                'exploreRemote'           : ":exploreremote",
                'session'                 : ":session",
                'workoutdetails'          : ":workoutdetails",
                'workoutplanner'          : ":workoutplanner",
                'exceptions'              : ":exceptions",
                'diveModes'               : ":divemodes",
                'diveCustomizationDomain' : ":divecustomizationdomain",
                'diveCustomization'       : ":divecustomization",
                'divePlannerDomain'       : ":diveplannerdomain",
                'divePlanner'             : ":diveplanner",
                'device'                  : ":devicebase",
                'deviceonboarding'        : ":deviceonboarding",
                'analyticschina'          : ":analyticschina",
                'diary'                   : ":diary",
                'diaryDomain'             : ":diarydomain",
                'elevationdata'           : ":elevationdata",
                'questionnaire'           : ":questionnaire",
                'suuntoPlusStore'         : ":suuntoplusstore",
                'suuntoPlusUi'            : ":suuntoplusui",
                'watchdebug'              : ":watchdebug",
                'divetrack'               : ":divetrack",
                "menstrualCycleOnboarding": ":menstrualcycleonboarding",
                "menstrualCycleDomain"    : ":menstrualcycledomain",
                "menstrualCycleDatasource": ":menstrualcycledatasource",
                "menstrualCycleRemote"    : ":menstrualcycleremote",
                'musicManager'            : ":musicmanager",
                "sportMode"               : ":sportmode",
                'HeadsetBesOtaLibrary'    : ":HeadsetBesOtaLibrary",
                'HeadsetJLOtaSdkLibrary'  : ":HeadsetJLOtaSdkLibrary",
                'HeadsetJLOtaCoreLibrary' : ":HeadsetJLOtaCoreLibrary",
                'HeadsetSoaLibrary'       : ":HeadsetSoaLibrary",
                'headset'                 : ":headset",
                "watchOfflineMusic"       : ":watchofflinemusic",
                "eventtracking"           : ":eventtracking",
                "sharingPlatform"         : ":sharingplatform",
                "sharingRedNoteLibrary"   : ":SharingRedNoteLibrary",
                "chartApi"                : ":chartapi",
                "chartImpl"               : ":chartimpl",
                "tutorialApi"             : ":tutorialapi",
                "tutorialImpl"            : ":tutorialimpl",
                "croplib"                 : ":croplib",
    ]
}
