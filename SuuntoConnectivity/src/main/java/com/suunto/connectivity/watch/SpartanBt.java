package com.suunto.connectivity.watch;

import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.Gson;
import com.movesense.mds.MdsException;
import com.movesense.mds.MdsResponse;
import com.squareup.moshi.JsonAdapter;
import com.squareup.moshi.Moshi;
import com.squareup.moshi.Types;
import com.stt.android.domain.firmware.Version;
import com.stt.android.eventtracking.EventTracker;
import com.stt.android.moshi.MoshiProvider;
import com.stt.android.timeline.TimelineResourceLocalDataSource;
import com.stt.android.timeline.WeChatTimelineResourceLocalDataSource;
import com.stt.android.timeline.entity.SuuntoSleepDataContract;
import com.suunto.connectivity.SpartanUserSettings;
import com.suunto.connectivity.battery.BatteryLevel;
import com.suunto.connectivity.battery.ChargingState;
import com.suunto.connectivity.battery.UsbCableState;
import com.suunto.connectivity.burypoint.BuriedPointResource;
import com.suunto.connectivity.debug.DebugGeoCoordinate;
import com.suunto.connectivity.files.DeleteFileMdsRequest;
import com.suunto.connectivity.files.FileListMdsRequest;
import com.suunto.connectivity.files.FileListMdsResponse;
import com.suunto.connectivity.files.GetFileMdsRequest;
import com.suunto.connectivity.files.PutFileMdsRequest;
import com.suunto.connectivity.gps.GpsDataResource;
import com.suunto.connectivity.gps.GpsFileManager;
import com.suunto.connectivity.gps.entities.FilePathWithIndex;
import com.suunto.connectivity.gps.entities.GpsFilePath;
import com.suunto.connectivity.gps.entities.GpsFormat;
import com.suunto.connectivity.gps.entities.GpsFormatType;
import com.suunto.connectivity.gps.entities.GpsTimestamp;
import com.suunto.connectivity.gps.entities.NagigationSystemResponse;
import com.suunto.connectivity.gps.entities.NavigationSystem;
import com.suunto.connectivity.gps.entities.NavigationSystemArray;
import com.suunto.connectivity.location.LastKnownLocationRequest;
import com.suunto.connectivity.notifications.MdsNotificationCategoryEnabled;
import com.suunto.connectivity.notifications.MdsNotificationEnabled;
import com.suunto.connectivity.notifications.MdsPredefinedLabels;
import com.suunto.connectivity.notifications.MdsPredefinedRepliesRequest;
import com.suunto.connectivity.notifications.NotificationsDevice;
import com.suunto.connectivity.offlinemaps.AreaUnderDownloadDeletedRequest;
import com.suunto.connectivity.recovery.RecoveryDataResource;
import com.suunto.connectivity.recovery.SuuntoRecoveryDataContract;
import com.suunto.connectivity.repository.commands.InstallSelectedFirmwareResponse;
import com.suunto.connectivity.sdsmanager.MdsRx;
import com.suunto.connectivity.sdsmanager.model.MdsArrayValue;
import com.suunto.connectivity.sdsmanager.model.MdsContent;
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo;
import com.suunto.connectivity.sdsmanager.model.MdsValue;
import com.suunto.connectivity.settings.Setting;
import com.suunto.connectivity.settings.SettingsResource;
import com.suunto.connectivity.sleep.SleepResource;
import com.suunto.connectivity.sportsappsettings.LockSportsAppContract;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_CONTRACT_EMPTY;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_URI_AUTHORITY;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor;
import com.suunto.connectivity.suuntoplusguide.MdsPluginInstallAidResponse;
import com.suunto.connectivity.sync.SynchronizerStorage;
import com.suunto.connectivity.sync.WatchSynchronizer;
import com.suunto.connectivity.systemevents.SystemEventsResource;
import com.suunto.connectivity.trainingzone.TrainingZoneResource;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncExercises;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncProgress;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncRecovery;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncThresholds;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncTraining;
import com.suunto.connectivity.trenddata.SuuntoTrendDataContract;
import com.suunto.connectivity.trenddata.TrendDataResource;
import com.suunto.connectivity.util.SupportedDevices;
import com.suunto.connectivity.watch.activitydata.ActivityDataHolder;
import com.suunto.connectivity.watch.backendstatus.LogEntryFlags;
import static com.suunto.connectivity.watch.backendstatus.LogEntryFlagsKt.SUMMARY_ALL_SYNCED;
import static com.suunto.connectivity.watch.backendstatus.LogEntryFlagsKt.VALUES_ALL_SYNCED;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareInfoResult;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareSuggestContract;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareSuggestResult;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareTransferContract;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareTransferStatus;
import com.suunto.connectivity.watch.sportmodes.SportModesDataHolder;
import com.suunto.connectivity.watch.time.SpartanTimeZoneInfo;
import com.suunto.connectivity.watch.time.TimeZoneSetting;
import com.suunto.connectivity.wifi.AccessPointCollectionElement;
import com.suunto.connectivity.wifi.AccessPointEncryption;
import com.suunto.connectivity.wifi.AccessPointSecurity;
import com.suunto.connectivity.wifi.ConnectWifiNetworkMdsRequest;
import com.suunto.connectivity.wifi.GetSavedWifiNetworkIndicesMdsResponse;
import com.suunto.connectivity.wifi.GetScannedWifiNetworkMdsRequest;
import com.suunto.connectivity.wifi.SetWifiGeneralSettingsMdsQuery;
import com.suunto.connectivity.wifi.WifiAP;
import com.suunto.connectivity.wifi.WifiResult;
import com.suunto.connectivity.wifi.WifiResultReasonAdapter;
import com.suunto.connectivity.zonesense.ZoneSenseBaselineValuesSyncData;
import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.time.Clock;
import java.time.DayOfWeek;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.functions.Func1;
import timber.log.Timber;

/**
 * Device abstraction for Spartan devices that uses Bluetooth
 */
public class SpartanBt extends WatchBt {

    private static final String TREND_DATA_URI = MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Activity/%s/Entries";
    private static final String RECOVERY_DATA_URI = "Activity/Moments/Sync/Data";
    private static final String GNSS_FORMAT_URI =
        MDS_SCHEME_PREFIX + "%s/Device/GNSS/ExtendedEphemerisData/Format";
    private static final String NAVIGATION_SYSTEMS_URI =
        MDS_SCHEME_PREFIX + "%s/Device/GNSS/NavigationSystem";
    private static final String SLEEP_SAMPLES_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Sleep/%s/Entries";
    private static final String SYSTEM_EVENTS_URI =
        MDS_SCHEME_PREFIX + "MDS/Analytics/%s/Systemevents";
    private static final String FIRMWARE_TRANSFER_GET_UPLOADED_FIRMWARES =
        MDS_SCHEME_PREFIX + "%s/Update/Info";
    private static final String FIRMWARE_TRANSFER_STATUS_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Firmware/Transfer/%s/Status";
    private static final String FIRMWARE_TRANSFER_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Firmware/Transfer/%s";
    private static final String FIRMWARE_SUGGEST_URI =
        MDS_SCHEME_PREFIX + "%s/Update/Suggest";
    private static final String FIRMWARE_SELECT_URI =
        MDS_SCHEME_PREFIX + "%s/Update/Select";
    public static final String INSTALL_SELECT_FIRMWARE_URI =
        MDS_SCHEME_PREFIX + "%s/Update/InstallSelectedFirmware";
    private static final String WATCH_LOGBOOK_FLAGS_URI = MDS_SCHEME_PREFIX +
        "%s/Logbook/byId/%d/Flags";
    private static final String PREDEFINED_REPLIES_URI = MDS_SCHEME_PREFIX +
        "%s/Message/History/Replies";
    public static final String FILE_LIST_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "FileSystem/%s/List";
    public static final String FILE_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "FileSystem/%s/File";
    public static final String WIFI_CONNECTIVITY_URI_WITHOUT_SCHEME =
        "%s/Device/Connectivity/Wifi";
    public static final String WIFI_CONNECTIVITY_URI =
        MDS_SCHEME_PREFIX + WIFI_CONNECTIVITY_URI_WITHOUT_SCHEME;
    public static final String WIFI_AP_SETTINGS_URI_WITHOUT_SCHEME =
        "%s/Settings/Wifi/AccessPoints";
    public static final String WIFI_AP_SETTINGS_URI =
        MDS_SCHEME_PREFIX + WIFI_AP_SETTINGS_URI_WITHOUT_SCHEME;
    public static final String WIFI_GENERAL_SETTINGS_URI =
        MDS_SCHEME_PREFIX + "%s/Settings/Wifi/GeneralSettings";
    public static final String WIFI_CLOUD_OFFLINE_MAPS_URL_URI =
        MDS_SCHEME_PREFIX + "%s/Settings/Wifi/Cloud/OfflineMaps/Url";
    public static final String WIFI_CLOUD_AUTH_TOKEN_URI =
        MDS_SCHEME_PREFIX + "%s/Settings/Wifi/Cloud/STTAuthorization";
    public static final String WIFI_ENABLED_URI_WITHOUT_SCHEME =
        "%s/Settings/Connectivity/Current/Wifi/Enabled";
    public static final String WIFI_ENABLED_URI =
        MDS_SCHEME_PREFIX + WIFI_ENABLED_URI_WITHOUT_SCHEME;
    public static final String INBOX_WIFI_ENABLED_URI =
        MDS_SCHEME_PREFIX + "%s/Settings/Wifi/Enabled";
    public static final String MAPS_DOWNLOAD_NOTIFY_SELECTION_CHANGED =
        MDS_SCHEME_PREFIX + "%s/MapsDownload/AreaSelectionChanged";
    public static final String MAPS_DOWNLOAD_NOTIFY_AREA_UNDER_DOWNLOAD_DELETED =
        MDS_SCHEME_PREFIX + "%s/MapsDownload/AreaUnderDownloadDeleted";
    public static final String MAPS_DOWNLOAD_NUMBER_OF_AREAS =
        MDS_SCHEME_PREFIX + "%s/MapsDownload/NumberOfAreas";
    public static final String POWER_URI_WITHOUT_SCHEME =
        "%s/Device/Power";
    public static final String LOCK_SPORTS_APP_URI =
        MDS_SCHEME_PREFIX + "%s/Zapp/%s/Lock";
    public static final String POWER_URI =
        MDS_SCHEME_PREFIX + POWER_URI_WITHOUT_SCHEME;
    public static final String PLUG_IN_INSTALL_AID_URI =
        MDS_SCHEME_PREFIX + "%s/Plugin/InstallAid";

    public static final String TRAINING_ZONE_TRAINING_DATA_URI =
        MDS_SCHEME_PREFIX + "%s/TrainingZone/Training";

    public static final String TRAINING_ZONE_PROGRESS_DATA_URI =
        MDS_SCHEME_PREFIX + "%s/TrainingZone/Progress";

    public static final String TRAINING_ZONE_RECOVERY_DATA_URI =
        MDS_SCHEME_PREFIX + "%s/TrainingZone/Recovery";

    public static final String TRAINING_ZONE_THRESHOLDS_DATA_URI =
        MDS_SCHEME_PREFIX + "%s/TrainingZone/Thresholds";

    public static final String DDFA_BASELINES_DATA_URI =
        MDS_SCHEME_PREFIX + "%s/Ddfa/Baselines";

    public static final String NOTIFICATION_ENABLED_URI = "%s/Settings/Ble/AncsEnabled";
    public static final String NOTIFICATION_CATEGORY_ENABLED_URI =
        "%s/Settings/Message/CategorySwitch";

    public static final String DEBUG_LOCATION_COORDINATES_URI =
        MDS_SCHEME_PREFIX + "%s/Device/Debug/Coordinates";

    public static final String CURRENT_WATCHFACE_ID_URI = "%s/Settings/Zapp/Watchface/Id";
    public static final String DEFAULT_WATCHFACE_ID_URI =
        MDS_SCHEME_PREFIX + "%s/Settings/Zapp/Watchface/Default";

    public static final String ACTIVITY_LAST_FOUR_WEEKS_DATA_URI =
        MDS_SCHEME_PREFIX + "%s/Activity/History/App/LastFourWeeks";

    private static final Long YEAR_2018_START_IN_MS = 1514764800000L;

    private final SuuntoBtDevice suuntoBtDevice;
    private final MdsRx mdsRx;
    private final Gson gson;
    private final SpartanSettings settings;
    private final SpartanDeviceSettings deviceSettings;
    private final TimeZoneSetting timeZoneInfo;
    protected WatchSynchronizer watchSynchronizer;
    private final GpsFileManager gpsFileManager;
    private final ActivityDataHolder activityDataHolder;
    private final SportModesDataHolder sportModesDataHolder;

    protected final Moshi moshi;
    private final JsonAdapter<FirmwareInfoResult> firmwareInfoAdapter;

    public SpartanBt(
        Context context,
        SuuntoBtDevice suuntoBtDevice,
        WatchConnector watchConnector,
        MdsRx mdsRx,
        Gson gson,
        BluetoothAdapter bluetoothAdapter,
        BtStateMonitor btStateMonitor,
        @Nullable NotificationsDevice notificationsDevice,
        SynchronizerStorage synchronizerStorage,
        GpsFileManager gpsFileManager,
        GpsDataResource gpsDataResource,
        SpartanSynchronizer.Injection synchronizerInjection,
        SupportedDevices supportedDevices,
        TimelineResourceLocalDataSource timelineResourceLocalDataSource,
        WeChatTimelineResourceLocalDataSource weChatTimelineResourceLocalDataSource,
        TrainingZoneResource trainingZoneResource,
        SettingsResource settingsResource,
        EventTracker eventTracker
    ) {
        super(context, suuntoBtDevice, watchConnector, mdsRx, bluetoothAdapter, btStateMonitor,
            notificationsDevice);

        this.suuntoBtDevice = suuntoBtDevice;
        this.mdsRx = mdsRx;
        this.gson = gson;
        this.moshi = MoshiProvider.INSTANCE.getInstance()
            .newBuilder()
            .add(new WifiResultReasonAdapter())
            .build();

        firmwareInfoAdapter =
            moshi.adapter(FirmwareInfoResult.class);
        this.settings = new SpartanSettings(suuntoBtDevice.getSerial(), mdsRx, gson, moshi);
        this.timeZoneInfo = new TimeZoneSetting(moshi, suuntoBtDevice.getSerial(), mdsRx);
        this.watchSynchronizer = new SpartanSynchronizer(
            this,
            gson,
            synchronizerStorage,
            gpsDataResource,
            new TrendDataResource(this, synchronizerStorage, moshi, timelineResourceLocalDataSource,
                weChatTimelineResourceLocalDataSource),
            new RecoveryDataResource(this, synchronizerStorage, moshi, timelineResourceLocalDataSource),
            new SleepResource(this, synchronizerStorage, moshi, timelineResourceLocalDataSource),
            new SystemEventsResource(this, synchronizerStorage, moshi),
            new BuriedPointResource(this, eventTracker),
            synchronizerInjection,
            supportedDevices,
            trainingZoneResource,
            settingsResource);
        this.gpsFileManager = gpsFileManager;
        this.activityDataHolder = new ActivityDataHolder(suuntoBtDevice.getSerial(), mdsRx, gson);
        this.sportModesDataHolder = new SportModesDataHolder(suuntoBtDevice.getSerial(), mdsRx, gson);
        this.deviceSettings = new SpartanDeviceSettings(moshi, suuntoBtDevice.getSerial(), mdsRx, gson);
    }

    @Override
    public WatchSynchronizer getWatchSynchronizer() {
        return watchSynchronizer;
    }

    @Override
    public Single<GpsTimestamp> getGNSSTimestamp() {
        String uri =
            String.format(Locale.US, GNSS_VERSION_URI, getSerial());
        return mdsRx.get(uri, MDS_CONTRACT_EMPTY)
            .map(response -> gson.fromJson(response, GpsTimestamp.class));
    }

    @Override
    public Single<MdsResponse> fetchTrendData(final long timestamp) {
        String uri = String.format(Locale.US, TREND_DATA_URI, getSerial());
        return mdsRx.getWithHeader(uri,
            moshi.adapter(SuuntoTrendDataContract.class)
                .toJson(new SuuntoTrendDataContract(timestamp)));
    }

    @Override
    public Single<MdsResponse> fetchRecoveryData(final long timestampSeconds) {
        return mdsRx.getWithHeader(buildUri(RECOVERY_DATA_URI),
            moshi.adapter(SuuntoRecoveryDataContract.class)
                .toJson(new SuuntoRecoveryDataContract(timestampSeconds)));
    }

    @Override
    public Single<MdsResponse> fetchSleepSamples(final long timestamp) {
        // It is agreed that we do not request data before 1.1.2018
        final long newerThan = Math.max(YEAR_2018_START_IN_MS, timestamp);
        // The watch has a circular buffer of size 120. So max 120 segments will be returned in data.
        String uri = String.format(Locale.US, SLEEP_SAMPLES_URI, getSerial());
        return mdsRx.getWithHeader(uri, moshi.adapter(SuuntoSleepDataContract.class)
            .toJson(new SuuntoSleepDataContract(newerThan)));
    }

    @Override
    public Single<GpsFormat> getGNSSFormat() {
        String uri =
            String.format(Locale.US, GNSS_FORMAT_URI, getSerial());

        return mdsRx.get(uri, MDS_CONTRACT_EMPTY)
            .map(response -> gson.fromJson(response, GpsFormat.class));
    }

    @Override
    public Single<List<NavigationSystem>> getNavigationSystems() {
        if (suuntoBtDevice.getDeviceType() == SuuntoDeviceType.Suunto9Peak) {
            // dolphin hack to return always all Navigation systems to ensure all files are uploaded
            return Single.just(Arrays.asList(NavigationSystem.values()));
        }

        String uri =
            String.format(Locale.US, NAVIGATION_SYSTEMS_URI, getSerial());

        return mdsRx.get(uri, MDS_CONTRACT_EMPTY)
            .map(response -> gson.fromJson(response, NagigationSystemResponse.class))
            .map(NagigationSystemResponse::getNavigationSystemArray)
            .map(NavigationSystemArray::getNavigationSystems);
    }

    @Override
    public Completable syncGpsFiles(List<NavigationSystem> navigationSystems, GpsFormatType gpsType) {
        String uri =
            String.format(Locale.US, GNSS_URI, suuntoBtDevice.getSerial());
        List<FilePathWithIndex> paths = gpsFileManager.getGpsFilePaths(navigationSystems, gpsType);
        if (paths != null) {
            GpsFilePath gpsFilePath = new GpsFilePath(paths);
            return mdsRx.put(uri, gson.toJson(gpsFilePath)).toCompletable();
        }
        throw new RuntimeException("No valid files");
    }

    @Override
    public Single<MdsResponse> fetchSystemEvents() {
        return mdsRx.getWithHeader(
            String.format(Locale.US, SYSTEM_EVENTS_URI, getSerial()),
            MDS_CONTRACT_EMPTY);
    }

    @Override
    public Observable<Integer> getUnsyncedMovesObservable() {
        String uri = String.format(Locale.US, UNSYNCED_LOGS_URI, getSerial());
        return mdsRx.subscribe(uri, Integer.class, true);
    }

    @Override
    public Observable<Integer> getWatchBusyObservable() {
        String uri =
            String.format(Locale.US, WATCH_BUSY_STATE_URI, getSerial());
        return mdsRx.subscribe(uri, Integer.class, true);
    }

    @Override
    public Single<FirmwareInfoResult> getUploadedFirmwares() {
        String uri =
            String.format(Locale.US, FIRMWARE_TRANSFER_GET_UPLOADED_FIRMWARES, getSerial());
        return mdsRx.get(uri, null)
            .flatMap((Func1<String, Single<FirmwareInfoResult>>) json -> {
                try {
                    return Single.just(firmwareInfoAdapter.fromJson(json));
                } catch (IOException e) {
                    Timber.e(e, "Failed to parse firmware information");
                    return Single.error(e);
                }
            });
    }

    @Override
    public Single<FirmwareSuggestResult> suggestFirmware(String firmwareFileName) {
        String uri =
            String.format(Locale.US, FIRMWARE_SUGGEST_URI, getSerial());
        JsonAdapter<FirmwareSuggestResult> adapter = moshi.adapter(FirmwareSuggestResult.class);
        String contract = gson.toJson(new FirmwareSuggestContract(firmwareFileName));
        return mdsRx.get(uri, contract)
            .flatMap((Func1<String, Single<FirmwareSuggestResult>>) json -> {
                try {
                    return Single.just(adapter.fromJson(json));
                } catch (IOException e) {
                    return Single.error(e);
                }
            });
    }

    @Override
    public Observable<FirmwareTransferStatus> getFirmwareTransferStatusObservable() {
        String uri =
            String.format(Locale.US, FIRMWARE_TRANSFER_STATUS_URI, getSerial());
        return mdsRx.subscribe(uri, FirmwareTransferStatus.class, false);
    }

    @Override
    public Completable startFirmwareTransfer(String path) {
        String uri =
            String.format(Locale.US, FIRMWARE_TRANSFER_URI, getSerial());
        String contract = gson.toJson(new FirmwareTransferContract(path));
        return mdsRx.post(uri, contract)
            .toCompletable();
    }

    @Override
    public Completable stopFirmwareTransfer(String path) {
        String uri =
            String.format(Locale.US, FIRMWARE_TRANSFER_URI, getSerial());
        String contract = gson.toJson(new FirmwareTransferContract(path));
        return mdsRx.delete(uri, contract)
            .toCompletable();
    }

    @Override
    public Completable requestFirmwareSelect(boolean forceUpdate, long packageId) {
        String uri =
            String.format(Locale.US, FIRMWARE_SELECT_URI, getSerial());
        String contract = gson.toJson(new FirmwareSelectContract(forceUpdate, packageId));
        return mdsRx.put(uri, contract)
            .toCompletable();
    }

    @Override
    public Single<InstallSelectedFirmwareResponse> installSelectedFirmware(long packageId,
        boolean forceUpdate) {
        String uri = String.format(Locale.US, INSTALL_SELECT_FIRMWARE_URI, getSerial());
        String contract = gson.toJson(new FirmwareSelectContract(forceUpdate, packageId));
        return mdsRx.put(uri, contract)
            .flatMap((Func1<String, Single<InstallSelectedFirmwareResponse>>) json ->
                Single.just(gson.fromJson(json, InstallSelectedFirmwareResponse.class))
            );
    }

    public Single<String> markEntryAsSynced(long entryId) {
        String uri =
            String.format(Locale.US, WATCH_LOGBOOK_FLAGS_URI,
                getSerial(),
                entryId);
        LogEntryFlags logEntryFlags = new LogEntryFlags(SUMMARY_ALL_SYNCED | VALUES_ALL_SYNCED);
        String contract = gson.toJson(logEntryFlags);
        return mdsRx.put(uri, contract);
    }

    /**
     * Gets the SpartanSettings object that allows easy access to getting or putting
     * settings values to the watch
     *
     * @return SpartanSettings object
     */
    @Override
    public SpartanSettings getSettings() {
        return settings;
    }

    @Override
    public SpartanDeviceSettings getDeviceSettings() {
        return deviceSettings;
    }

    @Override
    public ActivityDataHolder getActivityDataHolder() {
        return activityDataHolder;
    }

    @Override
    public SportModesDataHolder getSportModesDataHolder() {
        return sportModesDataHolder;
    }

    @NonNull
    @Override
    public Single<String> getLogbookJson() {
        String uri =
            String.format(Locale.US, LOGBOOK_ENTRIES_URI, getSerial());

        return mdsRx.get(uri, MDS_CONTRACT_EMPTY);
    }

    @Override
    public Completable getLogEntryJsonsToFiles(
        long entryId,
        SynchronizerStorage synchronizerStorage) {
        return getLogEntryJsonsToFiles(gson, entryId, synchronizerStorage);
    }

    @NonNull
    @Override
    public Completable updateTimeZoneInfo() {
        return Single.fromCallable(() ->
            SpartanTimeZoneInfo.buildNowInfo(
                Clock.systemDefaultZone()
            )
        ).flatMapCompletable(timeZoneInfo::setValue);
    }

    public TimestampSettings getTimestampSettings() {
        return settings.getTimestampSettings();
    }

    private Single<SpartanUserSettings.Builder> getSettingsBuilder() {
        return Single.just(getSettings())
            .flatMap(settings -> Single.zip(Single.just(SpartanUserSettings.builder()),
                settings.getGender().getValue().doOnError(
                    throwable -> Timber.w(throwable, "Gender")),
                settings.getBirthYear().getValue().doOnError(
                    throwable -> Timber.w(throwable, "getBirthYear")),
                settings.getWeight().getValue().doOnError(
                    throwable -> Timber.w(throwable, "getWeight")),
                settings.getMaxHR().getValue().doOnError(
                    throwable -> Timber.w(throwable, "getMaxHR")),
                settings.getUnitSystem().getValue().doOnError(
                    throwable -> Timber.w(throwable, "getUnitSystem")),
                (builder, gender, birthYear, weight, maxHR, unitSystem) ->
                    builder.gender(gender)
                        .birthYear(birthYear)
                        .weightInKilograms(weight)
                        .maxHR(maxHR)
                        .unitSystem(unitSystem)));
    }

    /**
     * Create single for getting setting value which may not be supported by the watch.
     *
     * @param setting Setting to be red from watch.
     * @return Single emitting the setting value or Float.NaN if setting does not exist.
     */
    private Single<Float> optionalValue(Setting<Float> setting) {
        return setting.getValue()
            .onErrorResumeNext(throwable -> {
                if (throwable instanceof MdsException) {
                    MdsException mdsException = (MdsException) throwable;
                    if (mdsException.getStatusCode() == 404
                        || mdsException.getStatusCode() == 503) {
                        return Single.just(Float.NaN);
                    }
                }
                return Single.error(throwable);
            });
    }

    public Single<SpartanUserSettings> getUnifiedSettings() {
        return getSettingsBuilder().map(SpartanUserSettings.Builder::build);
    }

    @Override
    public @NonNull Completable setPredefinedReplies(@NonNull List<String> predefinedReplies) {
        String uri = String.format(Locale.US, PREDEFINED_REPLIES_URI, getSerial());
        String contract = moshi.adapter(MdsPredefinedRepliesRequest.class)
            .toJson(new MdsPredefinedRepliesRequest(
                new MdsPredefinedLabels(predefinedReplies)));
        return mdsRx.put(uri, contract).toCompletable();
    }

    @Override
    public @NonNull Completable setFirstDayOfTheWeek(@NonNull DayOfWeek firstDayOfTheWeek) {
        return getSettings().updateFirstDayOfTheWeek(firstDayOfTheWeek);
    }

    @Override
    public @NonNull Single<FileListMdsResponse> getFileList(@NonNull String path) {
        String uri = String.format(Locale.US, FILE_LIST_URI, getSerial());
        return mdsRx
            .get(uri, encodeMdsRequest(new FileListMdsRequest(path), FileListMdsRequest.class))
            .flatMap(responseBody -> decodeMdsResponse(responseBody, FileListMdsResponse.class));
    }

    @Override
    public @NonNull Completable getFile(@NonNull String deviceFilePath,
        @NonNull String localFilePath) {
        String uri = String.format(Locale.US, FILE_URI, getSerial());
        return mdsRx
            .get(uri, encodeMdsRequest(new GetFileMdsRequest(deviceFilePath, localFilePath),
                GetFileMdsRequest.class))
            .toCompletable();
    }

    @Override
    public @NonNull Completable putFile(@NonNull String localFilePath,
        @NonNull String deviceFilePath) {
        String uri = String.format(Locale.US, FILE_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsRequest(new PutFileMdsRequest(localFilePath, deviceFilePath),
                PutFileMdsRequest.class))
            .toCompletable();
    }

    @Override
    public @NonNull Completable deleteFile(@NonNull String deviceFilePath) {
        String uri = String.format(Locale.US, FILE_URI, getSerial());
        return mdsRx
            .delete(uri, encodeMdsRequest(new DeleteFileMdsRequest(deviceFilePath),
                DeleteFileMdsRequest.class))
            .toCompletable();
    }

    @Override
    public @NonNull Observable<List<Integer>> getSavedWifiNetworkIndices() {
        String uri = String.format(Locale.US, WIFI_AP_SETTINGS_URI_WITHOUT_SCHEME, getSerial());
        return mdsRx
            .subscribe(uri, GetSavedWifiNetworkIndicesMdsResponse.class, true)
            .map(GetSavedWifiNetworkIndicesMdsResponse::getIndices);
    }

    @NonNull
    public Single<AccessPointCollectionElement> getSavedWifiNetwork(int index) {
        String uri = String.format(
            Locale.US, WIFI_AP_SETTINGS_URI + "/%d", getSerial(), index);
        return mdsRx
            .get(uri, null)
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, AccessPointCollectionElement.class));
    }

    @Override
    public @NonNull Single<WifiResult> connectWifiNetwork(@NonNull String ssid, @Nullable String key) {
        String uri = String.format(Locale.US, WIFI_CONNECTIVITY_URI + "/Connect", getSerial());
        return mdsRx
            .get(uri, encodeMdsRequest(new ConnectWifiNetworkMdsRequest(ssid, key),
                ConnectWifiNetworkMdsRequest.class))
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, WifiResult.class));
    }

    @Override
    public @NonNull Completable forgetWifiNetwork(int index) {
        String uri = String.format(
            Locale.US, WIFI_AP_SETTINGS_URI + "/%d", getSerial(), index);
        return mdsRx
            .delete(uri, null)
            .toCompletable();
    }

    @Override
    public @NonNull Completable setWifiGeneralSettings(@NonNull String countryCode) {
        String uri = String.format(Locale.US, WIFI_GENERAL_SETTINGS_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(new SetWifiGeneralSettingsMdsQuery(countryCode),
                SetWifiGeneralSettingsMdsQuery.class))
            .toCompletable();
    }

    @NonNull
    @Override
    public Completable setOfflineMapsUrl(@NonNull String url) {
        String uri = String.format(Locale.US, WIFI_CLOUD_OFFLINE_MAPS_URL_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(url, String.class))
            .toCompletable();
    }

    @NonNull
    @Override
    public Completable setAuthToken(@NonNull String token) {
        String uri = String.format(Locale.US, WIFI_CLOUD_AUTH_TOKEN_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(token, String.class))
            .toCompletable();
    }

    @NonNull
    @Override
    public Completable notifyAreaSelectionChanged() {
        String uri = String.format(Locale.US, MAPS_DOWNLOAD_NOTIFY_SELECTION_CHANGED, getSerial());
        return mdsRx
            .put(uri, null)
            .toCompletable();
    }

    @NonNull
    @Override
    public Completable notifyAreaUnderDownloadDeleted(@NonNull String areaId) {
        String uri = String.format(Locale.US, MAPS_DOWNLOAD_NOTIFY_AREA_UNDER_DOWNLOAD_DELETED, getSerial());
        return mdsRx
            .put(uri, encodeMdsRequest(new AreaUnderDownloadDeletedRequest(areaId), AreaUnderDownloadDeletedRequest.class))
            .toCompletable();
    }

    @NonNull
    @Override
    public Observable<Boolean> getWifiEnabledObservable() {
        String uri = String.format(Locale.US, WIFI_ENABLED_URI_WITHOUT_SCHEME, getSerial());
        return mdsRx.subscribe(uri, Boolean.class, true);
    }

    @NonNull
    @Override
    public Completable setWifiEnabled(@NonNull Boolean enabled) {
        MdsDeviceInfo deviceInfo = getCurrentState().getDeviceInfo();
        if (deviceInfo == null) {
            return Completable.error(new IllegalStateException("Cannot get the device info."));
        }
        String fwVersion = deviceInfo.getSwVersion();
        String uriPath =
            new Version(fwVersion).compareTo(new Version("2.40.38")) > 0 ? INBOX_WIFI_ENABLED_URI
                : WIFI_ENABLED_URI;
        String uri = String.format(Locale.US, uriPath, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(enabled, Boolean.class))
            .toCompletable();
    }

    @NonNull
    @Override
    public Completable enableInboxWifi() {
        String uri = String.format(Locale.US, INBOX_WIFI_ENABLED_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(true, Boolean.class))
            .toCompletable();
    }

    @NonNull
    @Override
    public Single<Integer> numberOfAreas() {
        String uri = String.format(Locale.US, MAPS_DOWNLOAD_NUMBER_OF_AREAS, getSerial());
        return mdsRx
            .get(uri, null)
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, Integer.class));
    }

    @Override
    public @NonNull Single<WifiResult> getWifiConnectionStatus() {
        String uri =
            String.format(Locale.US, WIFI_CONNECTIVITY_URI + "/Connection/Status", getSerial());
        return mdsRx
            .get(uri, null)
            .flatMap(
                responseBody -> decodeMdsResponseWrappedInContent(responseBody, WifiResult.class));
    }

    @Override
    public @NonNull Observable<WifiResult> getWifiConnectionStatusObservable() {
        String uri =
            String.format(Locale.US, WIFI_CONNECTIVITY_URI_WITHOUT_SCHEME + "/Connection/Status", getSerial());
        return mdsRx.subscribe(uri, WifiResult.class, true);
    }

    @Override
    public @NonNull Completable saveWifiNetwork(
        int index,
        @NonNull String ssid,
        @Nullable String key,
        @NonNull AccessPointSecurity security,
        @NonNull AccessPointEncryption encryption
    ) {
        String uri = String.format(Locale.US, WIFI_AP_SETTINGS_URI + "/%d", getSerial(), index);
        return mdsRx
            .put(
                uri,
                encodeMdsValue(
                    new AccessPointCollectionElement(
                        ssid, key, security.getValue(), encryption.getValue(), false, null
                    ),
                    AccessPointCollectionElement.class
                )
            )
            .toCompletable();
    }

    @Override
    public @NonNull Single<WifiResult> scanAvailableWifiNetworks() {
        String uri = String.format(Locale.US, WIFI_CONNECTIVITY_URI + "/ScanAccesspoints", getSerial());
        return mdsRx
            .get(uri, null)
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, WifiResult.class));
    }

    @Override
    public @NonNull Single<WifiAP> getWifiNetwork(int index) {
        String uri = String.format(Locale.US, WIFI_CONNECTIVITY_URI + "/Accesspoint", getSerial());
        return mdsRx
            .get(uri, encodeMdsRequest(new GetScannedWifiNetworkMdsRequest(index),
                GetScannedWifiNetworkMdsRequest.class))
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, WifiAP.class));
    }

    @Override
    public @NonNull Observable<BatteryLevel> getBatteryLevel() {
        String uri = String.format(Locale.US, POWER_URI_WITHOUT_SCHEME + "/Batterylevel", getSerial());
        return mdsRx
            .subscribe(uri, Integer.class, true)
            .map(BatteryLevel::new);
    }

    @Override
    public @NonNull Single<ChargingState> getChargingState() {
        String uri = String.format(Locale.US, POWER_URI + "/ChargingState", getSerial());
        return mdsRx
            .get(uri, null)
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, Integer.class))
            .map(value -> ChargingState.fromValue(value));
    }

    @Override
    public @NonNull Single<UsbCableState> getUsbCableState() {
        String uri = String.format(Locale.US, POWER_URI + "/VBUSDetected", getSerial());
        return mdsRx
            .get(uri, null)
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, Boolean.class))
            .map(UsbCableState::new);
    }

    @NonNull
    public Observable<UsbCableState> getUsbCableStateObservable() {
        String uri =
            String.format(Locale.US, POWER_URI_WITHOUT_SCHEME + "/VBUSDetected",
                getSerial());
        return mdsRx.subscribe(uri, Boolean.class, true)
            .map(UsbCableState::new);
    }

    @NonNull
    @Override
    public Completable lockSportsApp(@NonNull String pluginId) {
        String uri = String.format(Locale.US, LOCK_SPORTS_APP_URI, getSerial(), pluginId);
        String contract = encodeMdsRequest(new LockSportsAppContract(true), LockSportsAppContract.class);

        return mdsRx.put(uri, contract)
            .toCompletable()
            .onErrorResumeNext(err -> {
                if (err instanceof MdsException) {
                    Integer statusCode = ((MdsException)err).getStatusCode();
                    if (statusCode != null && statusCode == 422) {
                        Timber.d("Plug-in with ID %s already locked, no action needed", pluginId);
                        return Completable.complete();
                    }
                }
                return Completable.error(err);
            });
    }

    @NonNull
    @Override
    public Completable unlockSportsApp(@NonNull String pluginId) {
        String uri = String.format(Locale.US, LOCK_SPORTS_APP_URI, getSerial(), pluginId);
        String contract = encodeMdsRequest(new LockSportsAppContract(false), LockSportsAppContract.class);

        return mdsRx.put(uri, contract)
            .toCompletable()
            .onErrorResumeNext(err -> {
                if (err instanceof MdsException) {
                    Integer statusCode = ((MdsException)err).getStatusCode();
                    if (statusCode != null && statusCode == 404) {
                        Timber.d("Plug-in with ID %s already unlocked, no action needed", pluginId);
                        return Completable.complete();
                    }
                }
                return Completable.error(err);
            });
    }

    @NonNull
    @Override
    public Single<String> getZappPluginDirectory() {
        String uri = String.format(Locale.US, PLUG_IN_INSTALL_AID_URI, getSerial());

        return mdsRx.get(uri, "")
            .flatMap(responseBody -> decodeMdsResponse(responseBody, MdsPluginInstallAidResponse.class))
            .map(response -> response.getContent().getLocation());
    }

    @Override
    public Observable<Boolean> getNotificationEnabledObservable() {
        String uri = String.format(Locale.US, NOTIFICATION_ENABLED_URI, getSerial());
        return mdsRx.subscribe(uri, Boolean.class, true);
    }

    @Override
    public Single<String> putNotificationEnabled(boolean enabled) {
        String uri =
            String.format(Locale.US, MDS_SCHEME_PREFIX + NOTIFICATION_ENABLED_URI, getSerial());
        String contract =
            encodeMdsRequest(new MdsNotificationEnabled(enabled), MdsNotificationEnabled.class);
        return mdsRx.put(uri, contract);
    }

    @Override
    public Single<Boolean> getNotificationEnabled() {
        String uri =
            String.format(Locale.US, MDS_SCHEME_PREFIX + NOTIFICATION_ENABLED_URI, getSerial());
        return mdsRx.get(uri, MDS_CONTRACT_EMPTY)
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, Boolean.class));
    }

    @Override
    public Observable<MdsNotificationCategoryEnabled> getNotificationCategoryEnabledObservable() {
        String uri = String.format(Locale.US, NOTIFICATION_CATEGORY_ENABLED_URI, getSerial());
        return mdsRx.subscribe(uri, MdsNotificationCategoryEnabled.class, true);
    }

    @Override
    public Single<String> putNotificationCategoryEnabled(
        MdsNotificationCategoryEnabled categoryEnabled
    ) {
        String uri =
            String.format(Locale.US, MDS_SCHEME_PREFIX + NOTIFICATION_CATEGORY_ENABLED_URI, getSerial());
        String contract = encodeMdsValue(categoryEnabled, MdsNotificationCategoryEnabled.class);
        return mdsRx.put(uri, contract);
    }

    @NonNull
    @Override
    public Completable setDebugLocationCoordinates(@NonNull int latitude, @NonNull int longitude) {
        String uri = String.format(Locale.US, DEBUG_LOCATION_COORDINATES_URI, getSerial());
        return mdsRx
            .put(
                uri,
                encodeMdsValue(
                    new DebugGeoCoordinate(latitude, longitude),
                    DebugGeoCoordinate.class
                )
            )
            .toCompletable();
    }

    private <T> String encodeMdsRequest(T request, Class<T> requestClass) {
        return moshi.adapter(requestClass).toJson(request);
    }

    private <T> Single<T> decodeMdsResponse(String mdsResponse, Class<T> responseClass) {
        try {
            JsonAdapter<T> adapter = moshi.adapter(responseClass);
            return Single.just(adapter.fromJson(mdsResponse));
        } catch (IOException e) {
            return Single.error(e);
        }
    }

    protected  <T> String encodeMdsValue(T value, Class<T> requestClass) {
        ParameterizedType type = Types.newParameterizedType(MdsValue.class, requestClass);
        JsonAdapter<MdsValue<T>> adapter = moshi.adapter(type);
        return adapter.toJson(new MdsValue<>(value));
    }

    private <T> String encodeMdsArrayValue(List<T> items, Class<T> requestClass) {
        ParameterizedType type = Types.newParameterizedType(MdsArrayValue.class, requestClass);
        JsonAdapter<MdsArrayValue<T>> adapter = moshi.adapter(type);
        return adapter.toJson(new MdsArrayValue<>(items));
    }

    protected <T> Single<T> decodeMdsResponseWrappedInContent(String mdsResponse, Class<T> responseClass) {
        try {
            ParameterizedType type = Types.newParameterizedType(MdsContent.class, responseClass);
            JsonAdapter<MdsContent<T>> adapter = moshi.adapter(type);
            return Single.just(adapter.fromJson(mdsResponse).getContent());
        } catch (IOException e) {
            return Single.error(e);
        }
    }

    @Override
    public Completable setTrainingZoneTrainingData(@NonNull TrainingZoneSyncTraining training) {
        String uri = String.format(Locale.US, TRAINING_ZONE_TRAINING_DATA_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(training, TrainingZoneSyncTraining.class))
            .toCompletable();
    }

    public Completable setZoneSenseBaselineValuesData(
        @NonNull ZoneSenseBaselineValuesSyncData baselineValues
    ) {
        String uri = String.format(Locale.US, DDFA_BASELINES_DATA_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(baselineValues, ZoneSenseBaselineValuesSyncData.class))
            .toCompletable();
    }

    @Override
    public Completable setTrainingZoneProgressData(@NonNull TrainingZoneSyncProgress progress) {
        String uri = String.format(Locale.US, TRAINING_ZONE_PROGRESS_DATA_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(progress, TrainingZoneSyncProgress.class))
            .toCompletable();
    }

    @Override
    public Completable setTrainingZoneRecoveryData(@NonNull TrainingZoneSyncRecovery recovery) {
        String uri = String.format(Locale.US, TRAINING_ZONE_RECOVERY_DATA_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(recovery, TrainingZoneSyncRecovery.class))
            .toCompletable();
    }

    @Override
    public Completable setTrainingZoneThresholdsData(@NonNull TrainingZoneSyncThresholds thresholds) {
        String uri = String.format(Locale.US, TRAINING_ZONE_THRESHOLDS_DATA_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(thresholds, TrainingZoneSyncThresholds.class))
            .toCompletable();
    }

    @Override
    public Completable setTrainingZoneExercisesData(@NonNull TrainingZoneSyncExercises exercises) {
        String uri = String.format(Locale.US, ACTIVITY_LAST_FOUR_WEEKS_DATA_URI, getSerial());
        return mdsRx
            .put(uri, encodeMdsValue(exercises, TrainingZoneSyncExercises.class))
            .toCompletable();
    }

    public Single<String> putSyncLastKnownLocation(LastKnownLocationRequest lastKnownLocationRequest) {
        String contract = moshi.adapter(LastKnownLocationRequest.class).toJson(lastKnownLocationRequest);
        String uri = String.format(Locale.US, LAST_KNOWN_LOCATION_URI, getSerial());
        return mdsRx.put(uri, contract)
            .doOnSuccess(response -> Timber.v("putSyncLastKnownLocation onSuccess: %s", response))
            .doOnError(error -> Timber.w(error, "putSyncLastKnownLocation onError."));
    }

    @Override
    public Observable<String> getCurrentWatchfaceId() {
        String uri = String.format(Locale.US, CURRENT_WATCHFACE_ID_URI, getSerial());
        return mdsRx.subscribe(uri, String.class, true);
    }

    @Override
    public Single<String> getDefaultWatchfaceId() {
        String uri = String.format(Locale.US, DEFAULT_WATCHFACE_ID_URI, getSerial());
        return mdsRx.get(uri, MDS_CONTRACT_EMPTY)
            .flatMap(responseBody -> decodeMdsResponseWrappedInContent(responseBody, String.class));
    }

    @Override
    public Single<String> setCurrentWatchfaceId(String watchfaceId) {
        String uri = String.format(Locale.US, MDS_SCHEME_PREFIX + CURRENT_WATCHFACE_ID_URI, getSerial());
        return mdsRx.put(uri, encodeMdsValue(watchfaceId, String.class));
    }

    @Override
    public Completable updateOtaManualDownloadFlag() {
        String uri = String.format(Locale.US, UPDATE_OTA_MANUAL_DOWNLOAD_FAG, getSerial());
        return mdsRx.put(uri, null).toCompletable();
    }
}
