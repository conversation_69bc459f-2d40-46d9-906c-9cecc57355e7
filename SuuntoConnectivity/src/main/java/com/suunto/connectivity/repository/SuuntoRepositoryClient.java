package com.suunto.connectivity.repository;

import android.companion.CompanionDeviceManager;
import android.content.ComponentName;
import android.content.Context;
import static android.content.Context.BIND_ALLOW_ACTIVITY_STARTS;
import static android.content.Context.BIND_AUTO_CREATE;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Messenger;
import android.os.Parcelable;
import android.os.RemoteException;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Pair;
import androidx.lifecycle.ProcessLifecycleOwner;
import com.google.gson.Gson;
import com.google.gson.JsonIOException;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSyntaxException;
import com.stt.android.logbook.SmlParser;
import com.stt.android.logbook.SmlZip;
import com.stt.android.logbook.SuuntoLogbookSamples;
import com.stt.android.logbook.SuuntoLogbookSummaryContent;
import com.stt.android.utils.ProcessHelpersKt;
import com.suunto.connectivity.DeviceNotFoundException;
import com.suunto.connectivity.FileBasedLogbookEntry;
import com.suunto.connectivity.Spartan;
import com.suunto.connectivity.SpartanUserSettings;
import com.suunto.connectivity.SuuntoQueryConsumer;
import com.suunto.connectivity.battery.BatteryLevel;
import com.suunto.connectivity.battery.ChargingState;
import com.suunto.connectivity.battery.UsbCableState;
import com.suunto.connectivity.btscanner.ScannedSuuntoBtDevice;
import com.suunto.connectivity.ota.UpdateOtaManualDownloadFlagQuery;
import com.suunto.connectivity.repository.commands.InstallSelectedFirmwareResponse;
import com.suunto.connectivity.repository.commands.SetUserUnitSystemQuery;
import com.suunto.connectivity.settings.UnitSystem;
import com.suunto.connectivity.watch.navigate.NavigationQueryConsumer;
import com.suunto.connectivity.runsportmodes.RunSportModesConsumer;
import com.suunto.connectivity.hrintensityzones.HrIntensityZonesConsumer;
import com.suunto.connectivity.logbook.Logbook;
import com.suunto.connectivity.logbook.json.LogbookEntriesJson;
import com.suunto.connectivity.notifications.AncsMessage;
import com.suunto.connectivity.notifications.NotificationPackageInfo;
import com.suunto.connectivity.notifications.SetPredefinedRepliesQueryConsumer;
import com.suunto.connectivity.offlinemusic.OfflineMusicQueryConsumer;
import com.suunto.connectivity.poi.POIsQueryConsumer;
import static com.suunto.connectivity.repository.SuuntoRepositoryUtils.parseJsonFromFile;
import com.suunto.connectivity.repository.commands.AddNotificationsPackageQuery;
import com.suunto.connectivity.repository.commands.AddNotificationsPackageResponse;
import com.suunto.connectivity.repository.commands.Broadcast;
import com.suunto.connectivity.repository.commands.CheckForOtaUpdatesResponse;
import com.suunto.connectivity.repository.commands.ClearConnectionInstabilityQuery;
import com.suunto.connectivity.repository.commands.ConnectResponse;
import com.suunto.connectivity.repository.commands.DebugCommands;
import com.suunto.connectivity.repository.commands.DeleteFileQuery;
import com.suunto.connectivity.repository.commands.DisableNotificationsPackageQuery;
import com.suunto.connectivity.repository.commands.DisableNotificationsPackageResponse;
import com.suunto.connectivity.repository.commands.DisconnectQuery;
import com.suunto.connectivity.repository.commands.DisconnectResponse;
import com.suunto.connectivity.repository.commands.EmptyResponse;
import com.suunto.connectivity.repository.commands.EnableInboxWifiQuery;
import com.suunto.connectivity.repository.commands.EnableNotificationsPackageQuery;
import com.suunto.connectivity.repository.commands.EnableNotificationsPackageResponse;
import com.suunto.connectivity.repository.commands.ErrorResponse;
import com.suunto.connectivity.repository.commands.FirmwareTransferStartResponse;
import com.suunto.connectivity.repository.commands.ForgetWifiNetworkQuery;
import com.suunto.connectivity.repository.commands.Get247ActivityValueQuery;
import com.suunto.connectivity.repository.commands.Get247ActivityValueResponse;
import com.suunto.connectivity.repository.commands.Get247HrEnabledQuery;
import com.suunto.connectivity.repository.commands.Get247HrEnabledResponse;
import com.suunto.connectivity.repository.commands.Get247TargetQuery;
import com.suunto.connectivity.repository.commands.Get247TargetResponse;
import com.suunto.connectivity.repository.commands.GetActiveDevicesQuery;
import com.suunto.connectivity.repository.commands.GetActiveDevicesResponse;
import com.suunto.connectivity.repository.commands.GetBatteryLevelQuery;
import com.suunto.connectivity.repository.commands.GetBatteryLevelResponse;
import com.suunto.connectivity.repository.commands.GetChargingStateQuery;
import com.suunto.connectivity.repository.commands.GetChargingStateResponse;
import com.suunto.connectivity.repository.commands.GetCoachEnabledQuery;
import com.suunto.connectivity.repository.commands.GetCoachEnabledResponse;
import com.suunto.connectivity.repository.commands.GetFileListQuery;
import com.suunto.connectivity.repository.commands.GetFileListResponse;
import com.suunto.connectivity.repository.commands.GetFileQuery;
import com.suunto.connectivity.repository.commands.GetHrvEnabledQuery;
import com.suunto.connectivity.repository.commands.GetHrvEnabledResponse;
import com.suunto.connectivity.repository.commands.GetKnownNotificationsQuery;
import com.suunto.connectivity.repository.commands.GetKnownNotificationsResponse;
import com.suunto.connectivity.repository.commands.GetLogFilesQuery;
import com.suunto.connectivity.repository.commands.GetLogFilesResponse;
import com.suunto.connectivity.repository.commands.GetLogsQuery;
import com.suunto.connectivity.repository.commands.GetLogsResponse;
import com.suunto.connectivity.repository.commands.GetNotificationEnabledQuery;
import com.suunto.connectivity.repository.commands.GetNotificationEnabledResponse;
import com.suunto.connectivity.repository.commands.GetOrSetSettingsFileResponse;
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksCountQuery;
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksCountResponse;
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksQuery;
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksResponse;
import com.suunto.connectivity.repository.commands.GetSelectedFirmwareResponse;
import com.suunto.connectivity.repository.commands.GetSettingsFileQuery;
import com.suunto.connectivity.repository.commands.GetSetupPreferenceStateQuery;
import com.suunto.connectivity.repository.commands.GetSleepTrackingModeQuery;
import com.suunto.connectivity.repository.commands.GetSleepTrackingModeResponse;
import com.suunto.connectivity.repository.commands.GetSpO2NightlyEnabledQuery;
import com.suunto.connectivity.repository.commands.GetSpO2NightlyEnabledResponse;
import com.suunto.connectivity.repository.commands.GetUsbCableStateQuery;
import com.suunto.connectivity.repository.commands.GetUsbCableStateResponse;
import com.suunto.connectivity.repository.commands.GetWeeklyTargetDurationQuery;
import com.suunto.connectivity.repository.commands.GetWeeklyTargetDurationResponse;
import com.suunto.connectivity.repository.commands.GetZappPluginDirectoryQuery;
import com.suunto.connectivity.repository.commands.GetZappPluginDirectoryResponse;
import com.suunto.connectivity.repository.commands.GoalType;
import com.suunto.connectivity.repository.commands.ImportWorkoutFromFileQuery;
import com.suunto.connectivity.repository.commands.LockOrUnlockSportsAppQuery;
import com.suunto.connectivity.repository.commands.MarkAsSyncedQuery;
import com.suunto.connectivity.repository.commands.NotifyAreaSelectionChangedQuery;
import com.suunto.connectivity.repository.commands.NotifyAreaUnderDownloadDeletedQuery;
import com.suunto.connectivity.repository.commands.NumberOfAreasQuery;
import com.suunto.connectivity.repository.commands.NumberOfAreasResponse;
import com.suunto.connectivity.repository.commands.OTAUpdateActionQuery;
import com.suunto.connectivity.repository.commands.OTAUpdateActivatedResponse;
import com.suunto.connectivity.repository.commands.OTAUpdateDisabledResponse;
import com.suunto.connectivity.repository.commands.ObserveWifiEnabledQuery;
import com.suunto.connectivity.repository.commands.ObserveWifiEnabledResponse;
import com.suunto.connectivity.repository.commands.PairQuery;
import com.suunto.connectivity.repository.commands.PostNotificationQuery;
import com.suunto.connectivity.repository.commands.PutFileQuery;
import com.suunto.connectivity.repository.commands.PutNotificationCategoryEnabledQuery;
import com.suunto.connectivity.repository.commands.PutNotificationCategoryEnabledResponse;
import com.suunto.connectivity.repository.commands.PutNotificationEnabledQuery;
import com.suunto.connectivity.repository.commands.PutNotificationEnabledResponse;
import com.suunto.connectivity.repository.commands.PutSetupPreferenceStateQuery;
import com.suunto.connectivity.repository.commands.Query;
import com.suunto.connectivity.repository.commands.RegisterClientQuery;
import com.suunto.connectivity.repository.commands.RegisterClientResponse;
import com.suunto.connectivity.repository.commands.RemoveNotificationQuery;
import com.suunto.connectivity.repository.commands.ReportAppProcessForegroundQuery;
import com.suunto.connectivity.repository.commands.ResetConnectionQuery;
import com.suunto.connectivity.repository.commands.ResetConnectionResponse;
import com.suunto.connectivity.repository.commands.Response;
import com.suunto.connectivity.repository.commands.SaveWifiNetworkQuery;
import com.suunto.connectivity.repository.commands.SaveWifiNetworkResponse;
import com.suunto.connectivity.repository.commands.ScanAvailableWifiNetworksQuery;
import com.suunto.connectivity.repository.commands.ScanAvailableWifiNetworksResponse;
import com.suunto.connectivity.repository.commands.SelectFirmwareResponse;
import com.suunto.connectivity.repository.commands.ServiceStabilityQuery;
import com.suunto.connectivity.repository.commands.ServiceStabilityResponse;
import com.suunto.connectivity.repository.commands.Set247HrEnabledQuery;
import com.suunto.connectivity.repository.commands.Set247HrEnabledResponse;
import com.suunto.connectivity.repository.commands.Set247TargetQuery;
import com.suunto.connectivity.repository.commands.Set247TargetResponse;
import com.suunto.connectivity.repository.commands.SetAuthTokenQuery;
import com.suunto.connectivity.repository.commands.SetCoachEnabledQuery;
import com.suunto.connectivity.repository.commands.SetCoachEnabledResponse;
import com.suunto.connectivity.repository.commands.SetFirstDayOfTheWeekQuery;
import com.suunto.connectivity.repository.commands.SetFirstDayOfTheWeekResponse;
import com.suunto.connectivity.repository.commands.SetHrvEnabledQuery;
import com.suunto.connectivity.repository.commands.SetHrvEnabledResponse;
import com.suunto.connectivity.repository.commands.SetOfflineMapsUrlQuery;
import com.suunto.connectivity.repository.commands.SetPredefinedRepliesQuery;
import com.suunto.connectivity.repository.commands.SetPredefinedRepliesResponse;
import com.suunto.connectivity.repository.commands.SetSettingsFileQuery;
import com.suunto.connectivity.repository.commands.SetSleepTrackingModeQuery;
import com.suunto.connectivity.repository.commands.SetSleepTrackingModeResponse;
import com.suunto.connectivity.repository.commands.SetSpO2NightlyEnabledQuery;
import com.suunto.connectivity.repository.commands.SetSpO2NightlyEnabledResponse;
import com.suunto.connectivity.repository.commands.SetUserBirthYearQuery;
import com.suunto.connectivity.repository.commands.SetUserGenderQuery;
import com.suunto.connectivity.repository.commands.SetUserHeightQuery;
import com.suunto.connectivity.repository.commands.SetUserMaxHRQuery;
import com.suunto.connectivity.repository.commands.SetUserRestHRQuery;
import com.suunto.connectivity.repository.commands.SetUserSettingResponse;
import com.suunto.connectivity.repository.commands.SetUserWeightQuery;
import com.suunto.connectivity.repository.commands.SetWeeklyTargetDurationQuery;
import com.suunto.connectivity.repository.commands.SetWeeklyTargetDurationResponse;
import com.suunto.connectivity.repository.commands.SetWifiEnabledQuery;
import com.suunto.connectivity.repository.commands.SetWifiGeneralSettingsQuery;
import com.suunto.connectivity.repository.commands.SetupPreferenceInfo;
import com.suunto.connectivity.repository.commands.SleepTrackingMode;
import com.suunto.connectivity.repository.commands.StartLoggingQuery;
import com.suunto.connectivity.repository.commands.StartLoggingResponse;
import com.suunto.connectivity.repository.commands.StopLoggingQuery;
import com.suunto.connectivity.repository.commands.StopLoggingResponse;
import com.suunto.connectivity.repository.commands.StopOtaUpdateResponse;
import com.suunto.connectivity.repository.commands.SubscribeSetupPreferenceCancelQuery;
import com.suunto.connectivity.repository.commands.SyncDeviceQuery;
import com.suunto.connectivity.repository.commands.SyncDeviceResponse;
import com.suunto.connectivity.repository.commands.SyncTrainingZoneQuery;
import com.suunto.connectivity.repository.commands.SyncTrainingZoneResponse;
import com.suunto.connectivity.repository.commands.UnpairQuery;
import com.suunto.connectivity.repository.commands.UnpairResponse;
import com.suunto.connectivity.repository.commands.UserLogoutQuery;
import com.suunto.connectivity.repository.commands.WatchStateUpdateBroadcast;
import com.suunto.connectivity.routes.RoutesQueryConsumer;
import com.suunto.connectivity.settings.WearDirection;
import com.suunto.connectivity.sportmodes.SportModesConsumer;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDeviceImpl;
import com.suunto.connectivity.suuntoplusguides.SyncSuuntoPlusGuidesQueryConsumer;
import com.suunto.connectivity.util.IOUtils;
import com.suunto.connectivity.watch.SpartanSyncResult;
import com.suunto.connectivity.watch.SyncStepResult;
import com.suunto.connectivity.watch.WatchState;
import com.suunto.connectivity.watch.time.SetFirstDayOfTheWeekQueryConsumer;
import com.suunto.connectivity.watchcontrol.WatchControlConsumer;
import com.suunto.connectivity.widget.GetWidgetsQueryConsumer;
import com.suunto.connectivity.widget.SetWidgetsQueryConsumer;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.Reader;
import java.io.SyncFailedException;
import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.Subscription;
import rx.exceptions.Exceptions;
import rx.functions.Func1;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;
import rx.subjects.PublishSubject;
import rx.subjects.Subject;
import timber.log.Timber;

/**
 * Use this class to talk to {@link SuuntoRepositoryService}. It also provides offline access to
 * watch details when possible.
 */
@SuppressWarnings("WeakerAccess") // Used to avoid synthetic method accessors
public class SuuntoRepositoryClient {
    /**
     * Target we publish for clients to send messages to IncomingHandler.
     */
    final Messenger messengerFromService;
    final AtomicInteger nextMessageId = new AtomicInteger(1);
    /**
     * Map of known devices by their mac address.
     */
    final Map<String, Spartan> cachedDevices = new HashMap<>();
    /**
     * Emits {@link WatchStateUpdateBroadcast} messages. See
     * {@link #getOrCreateSpartan(SuuntoBtDevice, WatchState)} for an example of how to use
     */
    final Observable<WatchStateUpdateBroadcast> watchStateBroadcastsMessageObservable;
    final Context appContext;
    /**
     * Rather simple subject that contains the last known state of this service (See
     * {@link ServiceState})
     */
    final Subject<ServiceState, ServiceState> serviceStateSubject;
    final Gson gson = GsonFactory.buildGson();
    final SmlParser moshiParser = new SmlParser();
    final Observable<Broadcast> broadcastsMessageObservable;
    final Observable<ResponseMessage> responseMessageObservable;
    private final SportModesConsumer sportModesConsumer;
    private final RoutesQueryConsumer routesQueryConsumer;

    private final NavigationQueryConsumer navigationQueryConsumer;
    private final POIsQueryConsumer poisQueryConsumer;
    private final SyncSuuntoPlusGuidesQueryConsumer syncSuuntoPlusGuidesQueryConsumer;
    private final GetWidgetsQueryConsumer getWidgetsQueryConsumer;
    private final SetWidgetsQueryConsumer setWidgetsQueryConsumer;
    private final HrIntensityZonesConsumer hrIntensityZonesConsumer;
    @Nullable
    private AppLifecycleObserver appLifecycleObserver;
    private final OfflineMusicQueryConsumer offlineMusicQueryConsumer;
    private final RunSportModesConsumer runSportModesConsumer;
    private final WatchControlConsumer watchControlConsumer;

    public SportModesConsumer getSportModesConsumer() {
        return sportModesConsumer;
    }

    public RoutesQueryConsumer getRoutesQueryConsumer() {
        return routesQueryConsumer;
    }

    public NavigationQueryConsumer getNavigationQueryConsumer() {
        return navigationQueryConsumer;
    }

    public POIsQueryConsumer getPoisQueryConsumer() {
        return poisQueryConsumer;
    }

    public SyncSuuntoPlusGuidesQueryConsumer getSyncSuuntoPlusGuidesQueryConsumer() {
        return syncSuuntoPlusGuidesQueryConsumer;
    }

    public GetWidgetsQueryConsumer getGetWidgetsQueryConsumer() {
        return getWidgetsQueryConsumer;
    }

    public SetWidgetsQueryConsumer getSetWidgetsQueryConsumer() {
        return setWidgetsQueryConsumer;
    }

    public OfflineMusicQueryConsumer getOfflineMusicQueryConsumer() {
        return offlineMusicQueryConsumer;
    }

    public HrIntensityZonesConsumer getHrIntensityZonesConsumer() {
        return hrIntensityZonesConsumer;
    }

    public RunSportModesConsumer getRunSportModesConsumer() {
        return runSportModesConsumer;
    }

    public WatchControlConsumer getWatchControlConsumer() {
        return watchControlConsumer;
    }

    /**
     * Messenger for communicating with service.
     */
    @Nullable
    Messenger messengerToService = null;
    RepositoryConfiguration configuration;
    private Handler initialHandler;
    private Runnable bindRunnable;
    /**
     * Class for interacting with the main interface of the service.
     */
    private final ServiceConnection connection = new ServiceConnection() {
        public void onServiceConnected(ComponentName className, IBinder service) {
            // This is called when the connection with the service has been
            // established, giving us the service object we can use to
            // interact with the service.  We are communicating with our
            // service through an IDL interface, so get a client-side
            // representation of that from the raw service object.
            messengerToService = new Messenger(service);

            // We want to monitor the service for as long as we are
            // connected to it.
            registerClient();

            // refresh foreground in case this connection happened "late"
            if (appLifecycleObserver != null) {
                reportAppProcessForeground(appLifecycleObserver.isForeground());
            }
        }

        public void onServiceDisconnected(ComponentName className) {
            // This is called when the connection with the service has been
            // unexpectedly disconnected -- that is, its process crashed.
            messengerToService = null;
            Timber.d("onServiceDisconnected, try to bind SuuntoRepositoryService again");
            initialBindService();
        }
    };

    private void initialBindService() {
        if (initialHandler == null) {
            initialHandler = new Handler();
        }
        if (bindRunnable == null) {
            bindRunnable = this::connectToService;
        }
        initialHandler.postDelayed(bindRunnable, 1000);
    }

    private void connectToService() {
        // the problem here is that this class might be created while the app is in the background
        // if user has not enabled the companion association with our app, then we do not have the
        // permission to start a foreground service since Android 12, therefore we bind to it and
        // hope that it is running.
        int bindSuuntoRepositoryServiceFlags = BIND_AUTO_CREATE;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (appContext.getPackageManager().hasSystemFeature(PackageManager.FEATURE_COMPANION_DEVICE_SETUP)) {
                CompanionDeviceManager deviceManager = appContext.getSystemService(CompanionDeviceManager.class);
                if (deviceManager.getAssociations().isEmpty()) {
                    // user has not enabled companion
                    bindSuuntoRepositoryServiceFlags = Context.BIND_IMPORTANT | Context.BIND_ABOVE_CLIENT;
                }
            } else {
                // companion is not available on this device
                bindSuuntoRepositoryServiceFlags = Context.BIND_IMPORTANT | Context.BIND_ABOVE_CLIENT;
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            bindSuuntoRepositoryServiceFlags = bindSuuntoRepositoryServiceFlags | BIND_ALLOW_ACTIVITY_STARTS;
        }
        Timber.d("Binding to SuuntoRepositoryService from SuuntoRepositoryClient with flags: %d", bindSuuntoRepositoryServiceFlags);
        Intent serviceIntent = new Intent(appContext, SuuntoRepositoryService.class);
        boolean bindResult = appContext.bindService(serviceIntent, connection, bindSuuntoRepositoryServiceFlags);

        if (!bindResult) {
            Timber.d("Failed to bind SuuntoRepositoryService");
            initialBindService();
        }
    }

    /**
     * As soon as this is called we try to bind with service {@link SuuntoRepositoryService}
     */
    public SuuntoRepositoryClient(@NonNull Context appContext) {
        this.appContext = appContext;
        serviceStateSubject = BehaviorSubject.create(ServiceState.INITIALIZING).toSerialized();

        Subject<Broadcast, Broadcast> broadcastsSubject =
                PublishSubject.<Broadcast>create().toSerialized();
        broadcastsMessageObservable = broadcastsSubject
            .asObservable()
            .onBackpressureBuffer()
            .doOnNext(broadcast -> {
                    Timber.v("Observed broadcast message [" + broadcast + "]");
                }
            );

        Subject<ResponseMessage, ResponseMessage> responsesSubject =
                PublishSubject.<ResponseMessage>create().toSerialized();
        responseMessageObservable = responsesSubject.asObservable().onBackpressureBuffer();

        HandlerThread incomingHandlerThread =
                new HandlerThread("SuuntoRepositoryIncomingHandler");
        incomingHandlerThread.start();

        this.sportModesConsumer = new SportModesConsumer(this);
        this.routesQueryConsumer = new RoutesQueryConsumer(this);
        this.navigationQueryConsumer = new NavigationQueryConsumer(this);
        this.poisQueryConsumer = new POIsQueryConsumer(this);
        this.syncSuuntoPlusGuidesQueryConsumer = new SyncSuuntoPlusGuidesQueryConsumer(this);
        this.getWidgetsQueryConsumer = new GetWidgetsQueryConsumer(this);
        this.setWidgetsQueryConsumer = new SetWidgetsQueryConsumer(this);
        this.offlineMusicQueryConsumer = new OfflineMusicQueryConsumer(this);
        this.hrIntensityZonesConsumer = new HrIntensityZonesConsumer(this);
        this.runSportModesConsumer = new RunSportModesConsumer(this);
        this.watchControlConsumer = new WatchControlConsumer(this);

        SetPredefinedRepliesQueryConsumer predefinedRepliesConsumer =
            new SetPredefinedRepliesQueryConsumer(this);
        SetFirstDayOfTheWeekQueryConsumer setFirstDayOfTheWeekQueryConsumer =
            new SetFirstDayOfTheWeekQueryConsumer();

        List<SuuntoQueryConsumer> consumers = new ArrayList<>();
        consumers.add(sportModesConsumer);
        consumers.add(routesQueryConsumer);
        consumers.add(navigationQueryConsumer);
        consumers.add(poisQueryConsumer);
        consumers.add(predefinedRepliesConsumer);
        consumers.add(setFirstDayOfTheWeekQueryConsumer);
        consumers.add(syncSuuntoPlusGuidesQueryConsumer);
        consumers.add(getWidgetsQueryConsumer);
        consumers.add(setWidgetsQueryConsumer);
        consumers.add(offlineMusicQueryConsumer);
        consumers.add(hrIntensityZonesConsumer);
        consumers.add(runSportModesConsumer);
        messengerFromService = new Messenger(
                new IncomingHandler(incomingHandlerThread.getLooper(), appContext.getClassLoader(),
                        responsesSubject, broadcastsSubject, consumers));

        connectToService();

        // Filter and transform broadcast generic message to watch state ones
        watchStateBroadcastsMessageObservable =
                broadcastsMessageObservable
                        .filter(broadcast -> broadcast instanceof WatchStateUpdateBroadcast)
                        .map(broadcast -> {
                            Timber.v("Observed watch state update broadcast message [" + broadcast + "]");
                            return (WatchStateUpdateBroadcast) broadcast;
                        });


        if (ProcessHelpersKt.isMainProcess(getAppContext())) {
            appLifecycleObserver = new AppLifecycleObserver(this);
            ProcessLifecycleOwner.get().getLifecycle().addObserver(appLifecycleObserver);
        }
    }

    public Context getAppContext() {
        return appContext;
    }

    public void onDestroy() {
        appContext.unbindService(connection);
        if (initialHandler != null && bindRunnable != null) {
            initialHandler.removeCallbacks(bindRunnable);
        }
        initialHandler = null;
        bindRunnable = null;
    }

    void registerClient() {
        sendQuery(new RegisterClientQuery()).observeOn(Schedulers.computation())
                .map(response -> {
                    if (response instanceof RegisterClientResponse) {
                        return (RegisterClientResponse) response;
                    } else {
                        Timber.w("Invalid response type [" + response + "]");
                        throw new IllegalArgumentException("Invalid response type");
                    }
                })
                .doOnNext(registerClientResponse ->
                        configuration = registerClientResponse.getRepositoryConfiguration())
                .map(RegisterClientResponse::getAvailableDevices)
                .first()
                .toSingle()
                .subscribe(devices -> {
                            Map<String, Spartan> currentlyKnownDevices = new HashMap<>();
                            for (SpartanIpc device : devices) {
                                SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
                                Spartan spartan =
                                        getOrCreateSpartan(suuntoBtDevice, device.getWatchState());
                                currentlyKnownDevices.put(suuntoBtDevice.getMacAddress(), spartan);
                            }
                            serviceStateSubject.onNext(ServiceState.READY);
                            Timber.d("Currently known devices [" + currentlyKnownDevices + "]");
                        }, e -> {
                            Timber.w(e, "Unable to get the list of currently available devices");
                            serviceStateSubject.onError(new SuuntoRepositoryException("Service start failed"));
                        }
                );
    }

    /**
     * @return a {@link Single} that emits a boolean with true if the connection to the device was
     * successful
     */
    public Single<Boolean> connect(@NonNull Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
                sendQuery(new PairQuery(suuntoBtDevice, device.getScannedPairingState()))
                        .first()
                        .toSingle()
                        .flatMap(response -> {
                            if (response instanceof ConnectResponse) {
                                ConnectResponse connectResponse = (ConnectResponse) response;
                                if (!connectResponse.isConnected() && !connectResponse.getReason().isEmpty()) {
                                    return Single.error(
                                        new SuuntoRepositoryException(connectResponse.getReason()));
                                }
                                return Single.just(connectResponse.isConnected());
                            }
                            return Single.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    /**
     * @return a {@link Single} that emits a boolean with true if disconnect was successful
     */
    public Single<Boolean> disconnect(@NonNull final Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
                sendQuery(DisconnectQuery.create(suuntoBtDevice)).first()
                        .toSingle()
                        .flatMap(response -> {
                            if (response instanceof DisconnectResponse) {
                                return Single.just(((DisconnectResponse) response).isDisconnected());
                            }
                            return Single.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    /**
     * @return a {@link Single} that emits a boolean with true if unpairing was successful
     */
    public Single<Boolean> unpair(@NonNull final Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
                sendQuery(UnpairQuery.create(suuntoBtDevice)).first()
                        .toSingle()
                        .flatMap(response -> {
                            if (response instanceof UnpairResponse) {
                                return Single.just(((UnpairResponse) response).isUnpaired());
                            }
                            return Single.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    /**
     * Reset connection.
     */
    public Single<ResetConnectionResponse> resetConnection(@NonNull final Spartan device,
        @Nullable Integer timeoutBeforeReconnect) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new ResetConnectionQuery(suuntoBtDevice, timeoutBeforeReconnect))
                .first()
                .toSingle()
                .flatMap((Func1<Response, Single<ResetConnectionResponse>>) response -> {
                    if (response instanceof ResetConnectionResponse) {
                        return Single.just(((ResetConnectionResponse) response));
                    }
                    return Single.error(
                        new SuuntoRepositoryException("Invalid response [" + response + "]"));
                })
        );
    }

    /**
     * Get service stability.
     */
    public Single<ServiceStabilityResponse> getServiceStability() {
        return waitForServiceReady().andThen(
            sendQuery(new ServiceStabilityQuery())
                .first()
                .toSingle()
                .flatMap((Func1<Response, Single<ServiceStabilityResponse>>) response -> {
                    if (response instanceof ServiceStabilityResponse) {
                        return Single.just(((ServiceStabilityResponse) response));
                    }
                    return Single.error(
                        new SuuntoRepositoryException("Invalid response [" + response + "]"));
                })
        );
    }

    /**
     * Post a new notification
     *
     * @param ancsMessage AncsMessage to post
     * @return {@link Completable} which completes when SuuntoRepositoryService has completed
     * this request
     */
    public Completable postNotification(@NonNull AncsMessage ancsMessage) {
        return waitForServiceReady().andThen(
                sendQuery(new PostNotificationQuery(ancsMessage))
                        .first()
                        .toCompletable()
        );
    }

    /**
     * Remove previously posted notification
     *
     * @param messageId ID of the AncsMessage to be removed
     * @return {@link Completable} which completes when SuuntoRepositoryService has completed
     * this request
     */
    public Completable removeNotification(int messageId) {
        return waitForServiceReady().andThen(
                sendQuery(new RemoveNotificationQuery(messageId))
                        .first()
                        .toCompletable()
        );
    }

    /**
     * @return an {@link Observable} that emits {@link Response}s for
     * the given {@code query}. In theory a query could generate multiple responses
     */
    public Observable<Response> sendQuery(final Query query) {
        Observable<Response> deferredSend = Observable.defer(
                () -> createAndSendQuery(query));
        return deferredSend
                .subscribeOn(Schedulers.io());
    }

    /**
     * Waits for service ready and then sends the given query.
     * Ensures that a single response is received and it is of the expected type.
     * If an ErrorResponse is received, it is transformed to a SuuntoRepositoryException.
     *
     * @param query Query to send
     * @param expectedResponseClass Class of the expected response
     * @return Single emitting the response
     */
    private <T extends Response> Single<T> sendQueryWhenServiceReady(
        Query query,
        Class<T> expectedResponseClass
    ) {
        return waitForServiceReady()
            .andThen(
                sendQuery(query)
                    .first()
                    .toSingle()
                    .map(response -> {
                        if (expectedResponseClass.isInstance(response)) {
                            return expectedResponseClass.cast(response);
                        } if (response instanceof ErrorResponse) {
                            throw new SuuntoRepositoryException(
                                ((ErrorResponse) response).getMessage(),
                                ((ErrorResponse) response).getCause()
                            );
                        } else {
                            throw new SuuntoRepositoryException("Invalid response [" + response + "], expected [" + expectedResponseClass + "]");
                        }
                    })
            );
    }

    /**
     * Waits for service ready and then sends the given query.
     * Ensures that responses are of the expected type.
     * If an ErrorResponse is received, it is transformed to a SuuntoRepositoryException.
     *
     * @param query Query to send
     * @param expectedResponseClass Class of the expected response
     * @return Observable emitting the response
     */
    private <T extends Response> Observable<T> sendObservableQueryWhenServiceReady(
        Query query,
        Class<T> expectedResponseClass
    ) {
        return waitForServiceReady()
            .andThen(
                sendQuery(query)
                    .map(response -> {
                        if (expectedResponseClass.isInstance(response)) {
                            return expectedResponseClass.cast(response);
                        } if (response instanceof ErrorResponse) {
                            throw new SuuntoRepositoryException(
                                ((ErrorResponse) response).getMessage(),
                                ((ErrorResponse) response).getCause()
                            );
                        } else {
                            throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                        }
                    })
            );
    }

    /**
     * @return emits a {@link Observable} that contains the id for the new message being sent. If
     * the
     * connection to the service is lost then it emits error {@link SuuntoRepositoryException}
     */
    @NonNull
    private Observable<Response> createAndSendQuery(Query query) {
        int messageId = nextMessageId.getAndIncrement();
        Timber.v("Sending query message [%s] with message id [%d] to server", query.toString(), messageId);
        /*
         * Create caching response observable before sending request to service.
         * This is needed for ensuring that responses are not missed in any case.
         */
        final Observable<Response> cachingResponseObservable = filterResponseBy(messageId)
                .replay().refCount();
        final Subscription responseSubscription = cachingResponseObservable.subscribe();
        Bundle data = new Bundle();
        data.putParcelable(SuuntoRepositoryService.ArgumentKeys.ARG_DATA, query);
        Message message = Message.obtain(null, query.getMessageType(), messageId, 0);
        message.replyTo = messengerFromService;
        message.setData(data);
        if (messengerToService != null) {
            try {
                messengerToService.send(message);
                return cachingResponseObservable
                        .doOnUnsubscribe(responseSubscription::unsubscribe);
            } catch (RemoteException e) {
                responseSubscription.unsubscribe();
                return Observable.error(new SuuntoRepositoryException("Service not available"));
            }
        } else {
            Timber.w("messengerToService == null try to bind SuuntoRepositoryService");
            initialBindService();
            responseSubscription.unsubscribe();
            return Observable.error(new SuuntoRepositoryException("Service not available"));
        }
    }

    /**
     * @return and {@link Observable} that emits the responses only for the given {@code messageId}
     */
    @NonNull
    Observable<Response> filterResponseBy(final Integer messageId) {
        return responseMessageObservable.filter(responseMessage -> responseMessage.getMessageId() == messageId).map(ResponseMessage::getResponse);
    }

    public Single<Collection<Spartan>> getKnownDevices() {
        return waitForServiceReady().andThen(
                sendQuery(new GetActiveDevicesQuery())
                        .observeOn(Schedulers.computation())
                        .map(response -> {
                            if (!(response instanceof GetActiveDevicesResponse)) {
                                throw new IllegalArgumentException("Invalid response type");
                            }
                            return (GetActiveDevicesResponse) response;
                        })
                        .map(GetActiveDevicesResponse::getActiveDevices)
                        .first()
                        .toSingle()
                        .flatMap(spartanIpcs -> {
                            List<Spartan> activeDevices = new ArrayList<>();
                            for (SpartanIpc device : spartanIpcs) {
                                SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
                                Spartan spartan =
                                        getOrCreateSpartan(suuntoBtDevice, device.getWatchState());
                                activeDevices.add(spartan);
                            }
                            return Single.just((Collection<Spartan>) activeDevices);
                        })
        );
    }

    public Single<SuuntoBtDevice> getDevice(final String macAddress) {
        return waitForServiceReady().andThen(
                Single.fromCallable((Callable<Reader>) () -> {
                    try {
                        File deviceFile = new File(appContext.getFilesDir(),
                                configuration.getDeviceInfoPath(macAddress));
                        return new BufferedReader(new FileReader(deviceFile));
                    } catch (FileNotFoundException e) {
                        Timber.w(e, "Device [" + macAddress + "] file not found");
                        throw new DeviceNotFoundException(
                                "Device [" + macAddress + "] not " + "found");
                    }
                }).map(reader -> {
                    try {
                        return SuuntoBtDeviceImpl.copy(gson.fromJson(reader, SuuntoBtDeviceImpl.class));
                    } catch (JsonSyntaxException | JsonIOException e) {
                        Timber.w(e, "Invalid Json format for device information ["
                                + macAddress
                                + "]");
                        throw new DeviceNotFoundException("Device information corrupted",
                                e);
                    } finally {
                        IOUtils.closeQuietly(reader);
                    }
                })
        ).subscribeOn(Schedulers.io());
    }

    public Completable startLogging(File dir) {
        return waitForServiceReady().andThen(
                sendQuery(new StartLoggingQuery(dir.getAbsolutePath())).first()
                        .toSingle()
                        .flatMapCompletable(response -> {
                            if (response instanceof StartLoggingResponse) {
                                StartLoggingResponse loggingResponse = (StartLoggingResponse) response;
                                return loggingResponse.getError().isEmpty() ?
                                        Completable.complete() : Completable.error(
                                        new SuuntoRepositoryException(loggingResponse.getError()));
                            }
                            return Completable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    public Observable<File> stopLogging() {
        return waitForServiceReady().andThen(
                sendQuery(new StopLoggingQuery()).first()
                        .toSingle()
                        .flatMapObservable(response -> {
                            if (response instanceof StopLoggingResponse) {
                                StopLoggingResponse loggingResponse = (StopLoggingResponse) response;
                                List<File> files = new ArrayList<>();
                                for (String path : loggingResponse.getPaths()) {
                                    files.add(new File(path));
                                }

                                return Observable.from(files);
                            }

                            return Observable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    public Observable<File> getLogs(int logType) {
        return waitForServiceReady().andThen(
                sendQuery(new GetLogsQuery(logType))
                        .first()
                        .flatMap(response -> {
                            if (response instanceof GetLogsResponse) {
                                GetLogsResponse getLogsResponse = (GetLogsResponse) response;
                                if (getLogsResponse.getPath() != null) {
                                    return Observable.just(new File(getLogsResponse.getPath()));
                                } else {
                                    return Observable.just(null);
                                }
                            }

                            return Observable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    public Completable waitForServiceReady() {
        return serviceStateSubject.distinctUntilChanged()
                .filter(serviceState -> serviceState.equals(ServiceState.READY))
                .first()
                .toCompletable();
    }

    public Spartan toSpartan(SuuntoBtDevice suuntoBtDevice) {
        // We need an initial state in case this is the first time we see this device
        return getOrCreateSpartan(suuntoBtDevice, WatchState.builder().build());
    }

    /**
     * Has previously undefined device serial appeared?
     *
     * @param spartan        Current spartan instance.
     * @param suuntoBtDevice Suunto device received from connectivity.
     * @return True, is suuntoBtDevice contains serial which was previously undefined in spartan
     * instance.
     */
    private boolean SerialAppeared(@Nullable Spartan spartan,
        @NonNull SuuntoBtDevice suuntoBtDevice) {
        return spartan != null &&
            (spartan.getSerial() == null || spartan.getSerial().isEmpty()) &&
            (suuntoBtDevice.getSerial() != null && !suuntoBtDevice.getSerial().isEmpty());
    }

    @NonNull
    Spartan getOrCreateSpartan(@NonNull SuuntoBtDevice suuntoBtDevice, WatchState watchState) {
        if (!suuntoBtDevice.getDeviceType().isSpartan() &&
            !suuntoBtDevice.getDeviceType().isAmbit() &&
            !suuntoBtDevice.getDeviceType().isEon()) {
            throw new IllegalArgumentException("Provided suunto bt device is not a supported type");
        }

        // Check if we already have spartan instance for this device so return it directly
        final String macAddress = suuntoBtDevice.getMacAddress();
        Spartan spartan = cachedDevices.get(macAddress);
        if (SerialAppeared(spartan, suuntoBtDevice)) {
            // Serial was previously undefined, but found now.
            spartan.getSuuntoBtDevice().setSerial(suuntoBtDevice.getSerial());
        }
        if (spartan == null) {
            // This is a new device so we want to listen to its own state changes.
            Observable<WatchState> watchStateObservable = Observable.concat(
                    // Initially we want a default state
                    Observable.just(watchState),
                    // Then we'll just start listening for subsequent state changes and we're
                    // only interested in this device so let's filter by mac
                    watchStateBroadcastsMessageObservable.filter(
                            watchStateUpdateBroadcast -> {
                                String updateMacAddress =
                                        watchStateUpdateBroadcast.getWatchMacAddress();
                                Timber.v("Got watch state update for mac ["
                                        + updateMacAddress
                                        + "]"
                                        + " and I'm interested in mac ["
                                        + macAddress
                                        + "]");
                                return updateMacAddress.equals(macAddress);
                            }).map(WatchStateUpdateBroadcast::getWatchState))
                    // Since we want to keep the very last state we use replay here so whoever
                    // subscribes later gets the very latest watch state
                    .replay(1)
                    .autoConnect(1)
                    .doOnSubscribe(() ->
                            Timber.v("Somebody subscribed to watch state updates for mac ["
                                    + macAddress
                                    + "]"));
            spartan = new Spartan(suuntoBtDevice, this, watchStateObservable);
            if (suuntoBtDevice instanceof ScannedSuuntoBtDevice) {
                updateScannedPairingState((ScannedSuuntoBtDevice) suuntoBtDevice, spartan);
            }
            cachedDevices.put(suuntoBtDevice.getMacAddress(), spartan);
        } else {
            if (suuntoBtDevice instanceof ScannedSuuntoBtDevice) {
                updateScannedPairingState((ScannedSuuntoBtDevice) suuntoBtDevice, spartan);
            }
        }
        return spartan;
    }

    private void updateScannedPairingState(@NonNull ScannedSuuntoBtDevice suuntoBtDevice,
        @NonNull Spartan spartan) {
        spartan.setScannedPairingState(suuntoBtDevice.getAdvertisementPairingState());
    }

    // TODO: Use Gson directly instead of reading the whole String
    @NonNull
    String readJson(File jsonFile) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(jsonFile));
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            Timber.v("Reading Json ["
                    + sb
                    + "] from file ["
                    + jsonFile.getAbsolutePath()
                    + "]");
            return sb.toString();
        } catch (IOException e) {
            Timber.w("Unable to read json file [" + jsonFile.getAbsolutePath() + "]");
            throw Exceptions.propagate(e);
        } finally {
            IOUtils.closeQuietly(reader);
        }
    }

    public Single<String> getLatestGnssVersion(Spartan spartan) {
        return waitForServiceReady().andThen(
                Single.just(spartan.getSuuntoBtDevice().getMacAddress())
                        .map(macAddress -> {
                            String gnssVersionPath = configuration.getGnssVersionPath(macAddress);
                            File jsonFile = new File(appContext.getFilesDir(), gnssVersionPath);
                            return readJson(jsonFile);
                        })
                        .subscribeOn(Schedulers.io())
        );
    }

    public Single<List<Logbook.Entry>> getLatestLogbook(Spartan spartan) {
        return waitForServiceReady().andThen(
                Single.just(spartan.getSuuntoBtDevice().getMacAddress())
                        .map(macAddress -> {
                            String logbookPath = configuration.getLogbookPath(macAddress);
                            File jsonFile = new File(appContext.getFilesDir(), logbookPath);
                            return Pair.create(macAddress, jsonFile);
                        })
                        .map(macAndJson -> {
                            try {
                                LogbookEntriesJson entriesJson = parseJsonFromFile(gson, macAndJson.second,
                                        LogbookEntriesJson.class);
                                List<Logbook.Entry> entries = entriesJson != null ?
                                    entriesJson.getEntries() : Collections.emptyList();
                                String macAddress = macAndJson.first;
                                List<Logbook.Entry> localEntries = new ArrayList<>(entries.size());
                                for (Logbook.Entry entry : entries) {
                                    localEntries.add(new FileBasedLogbookEntry(entry, macAddress,
                                            SuuntoRepositoryClient.this));
                                }
                                return localEntries;
                            } catch (JsonParseException e) {
                                throw Exceptions.propagate(e);
                            }
                        })
                        .subscribeOn(Schedulers.io())
        );
    }

    public File getEntrySummaryFile(String macAddress, long id) {
        String path = configuration.getLogbookEntrySummaryPath(macAddress, id);
        return new File(appContext.getFilesDir(), path);
    }

    public File getEntrySamplesFile(String macAddress, long id) {
        String path = configuration.getLogbookEntrySamplesPath(macAddress, id);
        return new File(appContext.getFilesDir(), path);
    }

    public Single<SuuntoLogbookSummaryContent> getEntrySummaryContent(
        final String macAddress, final long id) {
        return waitForServiceReady().andThen(
            Single.fromCallable(() -> {
                File jsonFile = getEntrySummaryFile(macAddress, id);
                return moshiParser.parseSmlSummary(jsonFile);
            }).subscribeOn(Schedulers.io())
        );
    }

    public Single<SuuntoLogbookSamples> getEntrySamples(final String macAddress, final long id) {
        return waitForServiceReady().andThen(
                Single.fromCallable(() -> {
                    File jsonFile = getEntrySamplesFile(macAddress, id);
                    return moshiParser.parseSmlData(jsonFile);
                }).subscribeOn(Schedulers.io())
        );
    }

    public Single<SmlZip> getLogbookSMLJson(String macAddress, long id) {
        return waitForServiceReady().andThen(
            SuuntoRepositoryUtils.readSmlZip(appContext, configuration, macAddress, id)
        );
    }

    public Single<SpartanUserSettings> getUserSettings(final String macAddress) {
        return waitForServiceReady().andThen(
                Single.fromCallable(() -> {
                    String userSettingsPath = configuration.getUserSettingsFromWatchPath(macAddress);
                    File jsonFile = new File(appContext.getFilesDir(), userSettingsPath);
                    return parseJsonFromFile(gson, jsonFile, SpartanUserSettings.class);
                }).subscribeOn(Schedulers.io())
        );
    }

    public Single<SpartanSyncResult> synchronize(Spartan device, boolean isActivityDataOnly) {
        final SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
                device.getStateChangeObservable()
                        .first()
                        .toSingle()
                        .flatMap(watchState -> {
                            if (watchState.getConnectionState() != WatchState.ConnectionState.CONNECTED) {
                                return Single.error(new SyncFailedException("Watch not connected"));
                            }
                            return sendQuery(new SyncDeviceQuery(suuntoBtDevice, isActivityDataOnly)).first()
                                    .toSingle()
                                    .map(response -> {
                                        if (response instanceof SyncDeviceResponse) {
                                            return ((SyncDeviceResponse) response).getSyncResult();
                                        }
                                        throw new SuuntoRepositoryException(
                                                "Invalid response [" + response + "]");
                                    });
                        })
        );
    }

    public Single<SyncTrainingZoneResponse> requestTrainingZoneSync(Spartan device) {
        final SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        return sendQuery(new SyncTrainingZoneQuery(suuntoBtDevice)).first()
            .toSingle()
            .map(response -> {
                if (response instanceof SyncTrainingZoneResponse) {
                    return ((SyncTrainingZoneResponse) response);
                }
                throw new SuuntoRepositoryException(
                    "Invalid response [" + response + "]");
            });
    }

    /**
     * @return a {@link Single} that emits the latest known {@link SpartanSyncResult} or a
     * default {@link SpartanSyncResult} with {@link SyncResult.Value#UNKNOWN}.
     */
    public Single<SpartanSyncResult> getLatestSyncResult(final String macAddress) {
        return waitForServiceReady().andThen(
                Single.fromCallable(() -> {
                    String userSettingsPath = configuration.getLatestSyncResultPath(macAddress);
                    File jsonFile = new File(appContext.getFilesDir(), userSettingsPath);
                    return parseJsonFromFile(gson, jsonFile, SpartanSyncResult.class);
                }).map(result -> {
                    if (result != null) {
                        return result;
                    } else {
                        throw new JsonParseException("Empty spartan sync result file");
                    }
                })
                .onErrorReturn(throwable -> {
                    Timber.w(throwable, "Unable to read last sync results");
                    long now = System.currentTimeMillis();
                    return SpartanSyncResult.builder()
                            .trendDataResult(SyncStepResult.unknown())
                            .sleepResult(SyncStepResult.unknown())
                            .recoveryDataResult(SyncStepResult.unknown())
                            .gnssResult(SyncStepResult.unknown())
                            .settingsResult(SyncStepResult.unknown())
                            .systemEventsResult(SyncStepResult.unknown())
                            .macAddress(macAddress)
                            .logbookResult(
                                    LogbookSyncResult.builder().logbookResult(SyncResult.unknown())
                                        .build())
                            .syncEndTimestamp(now)
                            .syncStartTimestamp(now)
                            .build();
                }).subscribeOn(Schedulers.io())
        );
    }

    public Single<Boolean> getCoachEnabled(Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
                sendQuery(new GetCoachEnabledQuery(macAddress))
                        .first()
                        .toSingle()
                        .map(response -> {
                            if (response instanceof GetCoachEnabledResponse) {
                                return ((GetCoachEnabledResponse) response).isCoachEnabled();
                            }
                            throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                        })
        );
    }

    public Completable setCoachEnabled(Spartan device, boolean enabled) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
                sendQuery(new SetCoachEnabledQuery(macAddress, enabled))
                        .first()
                        .toSingle()
                        .flatMapCompletable(response -> {
                            if (response instanceof SetCoachEnabledResponse) {
                                return ((SetCoachEnabledResponse) response).isSuccessful()
                                        ? Completable.complete() : Completable.error(
                                        new SuuntoRepositoryException("Unable to set adaptive coach state"));
                            }
                            return Completable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    public Single<SleepTrackingMode> getSleepTrackingMode(Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
                sendQuery(new GetSleepTrackingModeQuery(macAddress))
                        .first()
                        .toSingle()
                        .map(response -> {
                            if (response instanceof GetSleepTrackingModeResponse) {
                                return ((GetSleepTrackingModeResponse) response).getSleepTrackingMode();
                            }
                            throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                        })
        );
    }

    @NonNull
    public Completable setSleepTrackingMode(@NonNull Spartan device, @NonNull SleepTrackingMode sleepTrackingMode) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new SetSleepTrackingModeQuery(macAddress, sleepTrackingMode))
                .first()
                .toSingle()
                .flatMapCompletable(response -> {
                    if (response instanceof SetSleepTrackingModeResponse) {
                        return ((SetSleepTrackingModeResponse) response).isSuccessful()
                            ? Completable.complete()
                            : Completable.error(new SuuntoRepositoryException("Unable to set sleep tracking mode"));
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    @NonNull
    public Single<Boolean> isSpO2NightlyEnabled(Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new GetSpO2NightlyEnabledQuery(macAddress))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof GetSpO2NightlyEnabledResponse) {
                        return ((GetSpO2NightlyEnabledResponse) response).getEnabled();
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    @NonNull
    public Completable setSpO2NightlyEnabled(@NonNull Spartan device, boolean enabled) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new SetSpO2NightlyEnabledQuery(macAddress, enabled))
                .first()
                .toSingle()
                .flatMapCompletable(response -> {
                    if (response instanceof SetSpO2NightlyEnabledResponse) {
                        return ((SetSpO2NightlyEnabledResponse) response).isSuccessful()
                            ? Completable.complete()
                            : Completable.error(new SuuntoRepositoryException("Unable to set SpO2 nightly enabled"));
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    @NonNull
    public Single<Boolean> isHrvEnabled(Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new GetHrvEnabledQuery(macAddress))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof GetHrvEnabledResponse) {
                        return ((GetHrvEnabledResponse) response).getEnabled();
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    @NonNull
    public Completable setHrvEnabled(@NonNull Spartan device, boolean enabled) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new SetHrvEnabledQuery(macAddress, enabled))
                .first()
                .toSingle()
                .flatMapCompletable(response -> {
                    if (response instanceof SetHrvEnabledResponse) {
                        return ((SetHrvEnabledResponse) response).isSuccessful()
                            ? Completable.complete()
                            : Completable.error(new SuuntoRepositoryException("Unable to set HRV enabled"));
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    @NonNull
    public Single<Boolean> is247HrEnabled(Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new Get247HrEnabledQuery(macAddress))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof Get247HrEnabledResponse) {
                        return ((Get247HrEnabledResponse) response).getEnabled();
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    @NonNull
    public Completable set247HrEnabled(@NonNull Spartan device, boolean enabled) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new Set247HrEnabledQuery(macAddress, enabled))
                .first()
                .toSingle()
                .flatMapCompletable(response -> {
                    if (response instanceof Set247HrEnabledResponse) {
                        return ((Set247HrEnabledResponse) response).isSuccessful()
                            ? Completable.complete()
                            : Completable.error(new SuuntoRepositoryException("Unable to set 247 HR enabled"));
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    public Single<Integer> get247Target(Spartan device, GoalType goalType) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new Get247TargetQuery(macAddress, goalType))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof Get247TargetResponse
                        && ((Get247TargetResponse) response).getDailyActivityType() == goalType) {
                        return ((Get247TargetResponse) response).getTarget();
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    public Completable set247Target(Spartan device, int targetValue, GoalType goalType) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new Set247TargetQuery(macAddress, targetValue, goalType))
                .first()
                .toSingle()
                .flatMapCompletable(response -> {
                    if (response instanceof Set247TargetResponse
                        && ((Set247TargetResponse) response).getDailyActivityType() == goalType) {
                        return ((Set247TargetResponse) response).isSuccessful()
                            ? Completable.complete() : Completable.error(
                            new SuuntoRepositoryException("Unable to set 247 target"));
                    }
                    return Completable.error(
                        new SuuntoRepositoryException("Invalid response [" + response + "]"));
                })
        );
    }

    public Single<Integer> getCurrentDailyActivityDataValue(Spartan device, GoalType goalType) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
            sendQuery(new Get247ActivityValueQuery(macAddress, goalType))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof Get247ActivityValueResponse
                        && ((Get247ActivityValueResponse) response).getDailyActivityType() == goalType) {
                        return ((Get247ActivityValueResponse) response).getValue();
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    public Single<Float> getWeeklyTargetDuration(Spartan device) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
                sendQuery(new GetWeeklyTargetDurationQuery(macAddress))
                        .first()
                        .toSingle()
                        .map(response -> {
                            if (response instanceof GetWeeklyTargetDurationResponse) {
                                return ((GetWeeklyTargetDurationResponse) response)
                                        .getWeeklyTargetDuration();
                            }
                            throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                        })
        );
    }

    public Completable setWeeklyTargetDuration(Spartan device, float duration) {
        SuuntoBtDevice suuntoBtDevice = device.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return waitForServiceReady().andThen(
                sendQuery(new SetWeeklyTargetDurationQuery(macAddress, duration))
                        .first()
                        .toSingle()
                        .flatMapCompletable(response -> {
                            if (response instanceof SetWeeklyTargetDurationResponse) {
                                return ((SetWeeklyTargetDurationResponse) response).isSuccessful()
                                        ? Completable.complete() : Completable.error(
                                        new SuuntoRepositoryException("Unable to set weekly target duration"));
                            }
                            return Completable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    /**
     * Clears WatchState.ConnectionQuality.INSTABILITY_DETECTED_RECONNECTION_DELAYED to
     * WatchState.ConnectionQuality.NORMAL. Makes connectivity service reconnect to watch
     * immediately if quality state was INSTABILITY_DETECTED_RECONNECTION_DELAYED.
     *
     * @return Completable for clearing connection instability.
     */
    public Completable clearConnectionInstability() {
        return waitForServiceReady().andThen(
                sendQuery(new ClearConnectionInstabilityQuery())
                        .first()
                        .toCompletable()
        );
    }

    public Single<List<NotificationPackageInfo>> getNotificationsKnownPackages() {
        return waitForServiceReady().andThen(
                sendQuery(new GetKnownNotificationsQuery()).first()
                        .toSingle()
                        .map(response -> {
                            if (response instanceof GetKnownNotificationsResponse) {
                                return ((GetKnownNotificationsResponse) response).getPackagesInfo();
                            }
                            throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                        })
        );
    }

    public Completable addPackage(String pkg) {
        return waitForServiceReady().andThen(
                sendQuery(new AddNotificationsPackageQuery(pkg)).first()
                        .toSingle()
                        .flatMapCompletable(response -> {
                            if (response instanceof AddNotificationsPackageResponse) {
                                return ((AddNotificationsPackageResponse) response).isSuccess()
                                        ? Completable.complete() : Completable.error(
                                        new SuuntoRepositoryException(
                                                "Unable to perform add notifications package query"));
                            }
                            return Completable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    public Completable enablePackage(String pkg) {
        return waitForServiceReady().andThen(
                sendQuery(new EnableNotificationsPackageQuery(pkg)).first()
                        .toSingle()
                        .flatMapCompletable(response -> {
                            if (response instanceof EnableNotificationsPackageResponse) {
                                return ((EnableNotificationsPackageResponse) response).isSuccess()
                                        ? Completable.complete() : Completable.error(
                                        new SuuntoRepositoryException(
                                                "Unable to perform enable notifications package query"));
                            }
                            return Completable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    public Completable disablePackage(String pkg) {
        return waitForServiceReady().andThen(
                sendQuery(new DisableNotificationsPackageQuery(pkg)).first()
                        .toSingle()
                        .flatMapCompletable(response -> {
                            if (response instanceof DisableNotificationsPackageResponse) {
                                return ((DisableNotificationsPackageResponse) response).isSuccess()
                                        ? Completable.complete() : Completable.error(
                                        new SuuntoRepositoryException(
                                                "Unable to perform disable notifications package query"));
                            }
                            return Completable.error(
                                    new SuuntoRepositoryException("Invalid response [" + response + "]"));
                        })
        );
    }

    /**
     * Notifies repository service, that workout has been synced to backend and there is no need to
     * reload workout from device. Also connected device is notified of synced workout. The
     * notification to device may be delayed until connections is resumed.
     *
     * @param macAddress Watch Mac address.
     * @param entryIds   List of entries to be marked as synced.
     * @return Completable for marking entries as synced.
     */
    public Completable markAsSynced(@NonNull String macAddress, @NonNull List<Long> entryIds) {
        return waitForServiceReady().andThen(
            sendQuery(new MarkAsSyncedQuery(macAddress, entryIds))
                .first()
                .toCompletable()
        );
    }

    /**
     * Notify repository service, that current user has logged out. Service deletes user specific
     * data.
     *
     * @return Completable for user logged out.
     */
    public Completable userLogout() {
        return waitForServiceReady().andThen(
            sendQuery(new UserLogoutQuery())
                .first()
                .toCompletable()
        );
    }

    void reportAppProcessForeground(boolean foreground) {
        waitForServiceReady().andThen(
            sendQuery(new ReportAppProcessForegroundQuery(foreground))
                .first()
                .toCompletable()
        ).subscribe(
            () -> Timber.d("reported app foreground=%s to connectivity", foreground),
            (e) -> Timber.w(e, "Error reporting foreground")
        );
    }

    private <T> Single<T> requestOTAUpdateAction(OTAUpdateActionQuery otaUpdateActionQuery,
        Class<T> resultType) {
        return waitForServiceReady().andThen(
            sendQuery(otaUpdateActionQuery).first()
                .toSingle()
                .map(response -> {
                    if (response.getClass().isAssignableFrom(resultType)) {
                        return resultType.cast(response);
                    } else {
                        throw new SuuntoRepositoryException(
                            "Invalid OTA update action response ["
                                + response
                                + "]. Expected: "
                                + resultType);
                    }
                }));
    }

    public Single<FirmwareTransferStartResponse> requestFirmwareTransferStart(
        SuuntoBtDevice suuntoBtDevice,
        Uri fileUri,
        @Nullable String firmwareVersion) {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.firmwareTransferStart(suuntoBtDevice, fileUri, firmwareVersion),
            FirmwareTransferStartResponse.class);
    }

    public Single<SelectFirmwareResponse> requestSelectFirmware(
        SuuntoBtDevice suuntoBtDevice,
        long packageId,
        boolean forceUpdate
    ) {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.selectFirmware(suuntoBtDevice, packageId, forceUpdate),
            SelectFirmwareResponse.class
        );
    }

    public Single<GetSelectedFirmwareResponse> getSelectedFirmware(SuuntoBtDevice suuntoBtDevice) {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.getSelectedFirmware(suuntoBtDevice),
            GetSelectedFirmwareResponse.class
        );
    }

    /**
     * Check OTA update status of the device.
     *
     * @param device      Device
     * @param deepLinkUri If null, latest OTA update is checked from the server. If not null, deep
     *                    link URI is negotiated with the device.
     * @return Check ota updates response.
     */
    public Single<CheckForOtaUpdatesResponse> checkForOtaUpdates(Spartan device,
        @Nullable Uri deepLinkUri) {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.checkForOtaUpdates(device.getSuuntoBtDevice(), deepLinkUri),
            CheckForOtaUpdatesResponse.class);
    }

    public Completable activateOtaUpdate() {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.otaUpdateActivated(),
            OTAUpdateActivatedResponse.class)
            .toCompletable();
    }

    public Completable disableOtaUpdate() {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.otaUpdateDisabled(),
            OTAUpdateDisabledResponse.class)
            .toCompletable();
    }

    public Single<StopOtaUpdateResponse> stopOtaUpdate(Spartan device) {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.stopOtaUpdate(device.getSuuntoBtDevice()),
            StopOtaUpdateResponse.class);
    }

    public Single<InstallSelectedFirmwareResponse> installSelectedFirmware(
        SuuntoBtDevice suuntoBtDevice,
        long packageId,
        boolean forceUpdate
    ) {
        return requestOTAUpdateAction(
            OTAUpdateActionQuery.installSelectedFirmware(suuntoBtDevice, packageId, forceUpdate),
            InstallSelectedFirmwareResponse.class);
    }

    public Completable importWorkoutFromFile(Uri workoutFileUri) {
        return waitForServiceReady().andThen(
            sendQuery(new ImportWorkoutFromFileQuery(workoutFileUri))
                .first()
                .toCompletable()
        );
    }

    public Single<GetOrSetSettingsFileResponse> getSettingsFile(Spartan spartan, Uri fileUri) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new GetSettingsFileQuery(fileUri, suuntoBtDevice))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof GetOrSetSettingsFileResponse) {
                        return ((GetOrSetSettingsFileResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    public Single<GetOrSetSettingsFileResponse> setSettingsFile(Spartan spartan, Uri fileUri) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetSettingsFileQuery(fileUri, suuntoBtDevice))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof GetOrSetSettingsFileResponse) {
                        return ((GetOrSetSettingsFileResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                })
        );
    }

    public Completable setPredefinedReplies(Spartan spartan, List<String> predefinedReplies) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetPredefinedRepliesQuery(predefinedReplies, suuntoBtDevice.getMacAddress()))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetPredefinedRepliesResponse) {
                        return ((SetPredefinedRepliesResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setFirstDayOfTheWeek(Spartan spartan, DayOfWeek firstDayOfTheWeek) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetFirstDayOfTheWeekQuery(firstDayOfTheWeek, suuntoBtDevice.getMacAddress()))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetFirstDayOfTheWeekResponse) {
                        return ((SetFirstDayOfTheWeekResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setUserGender(Spartan spartan, String gender) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetUserGenderQuery(suuntoBtDevice.getMacAddress(), gender))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetUserSettingResponse) {
                        return ((SetUserSettingResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setUserHeight(Spartan spartan, float height) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetUserHeightQuery(suuntoBtDevice.getMacAddress(), height))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetUserSettingResponse) {
                        return ((SetUserSettingResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setUserWeight(Spartan spartan, float weight) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetUserWeightQuery(suuntoBtDevice.getMacAddress(), weight))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetUserSettingResponse) {
                        return ((SetUserSettingResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setUserBirthYear(Spartan spartan, int birthYear) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetUserBirthYearQuery(suuntoBtDevice.getMacAddress(), birthYear))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetUserSettingResponse) {
                        return ((SetUserSettingResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setUserMaxHR(Spartan spartan, int maxHR) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetUserMaxHRQuery(suuntoBtDevice.getMacAddress(), maxHR))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetUserSettingResponse) {
                        return ((SetUserSettingResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setUserRestHR(Spartan spartan, int restHR) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetUserRestHRQuery(suuntoBtDevice.getMacAddress(), restHR))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetUserSettingResponse) {
                        return ((SetUserSettingResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Completable setUserUnitSystem(Spartan spartan, UnitSystem unitSystem) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return waitForServiceReady().andThen(
            sendQuery(new SetUserUnitSystemQuery(suuntoBtDevice.getMacAddress(), unitSystem))
                .first()
                .toSingle()
                .map(response -> {
                    if (response instanceof SetUserSettingResponse) {
                        return ((SetUserSettingResponse) response);
                    }
                    throw new SuuntoRepositoryException("Invalid response [" + response + "]");
                }).toCompletable()
        );
    }

    public Single<GetFileListResponse> getFileList(Spartan spartan, String path) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new GetFileListQuery(suuntoBtDevice.getMacAddress(), path),
            GetFileListResponse.class
        );
    }

    public Completable getFile(Spartan spartan, String deviceFilePath, String localFilePath) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new GetFileQuery(suuntoBtDevice.getMacAddress(), deviceFilePath, localFilePath),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable putFile(Spartan spartan, String localFilePath, String deviceFilePath) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new PutFileQuery(suuntoBtDevice.getMacAddress(), localFilePath, deviceFilePath),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable deleteFile(Spartan spartan, String deviceFilePath) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new DeleteFileQuery(suuntoBtDevice.getMacAddress(), deviceFilePath),
            EmptyResponse.class
        ).toCompletable();
    }

    public Observable<GetSavedWifiNetworksCountResponse> getSavedWifiNetworksCount(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendObservableQueryWhenServiceReady(
            new GetSavedWifiNetworksCountQuery(suuntoBtDevice.getMacAddress()),
            GetSavedWifiNetworksCountResponse.class
        );
    }

    public Single<GetSavedWifiNetworksResponse> getSavedWifiNetworks(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new GetSavedWifiNetworksQuery(suuntoBtDevice.getMacAddress()),
            GetSavedWifiNetworksResponse.class
        );
    }

    public Single<SaveWifiNetworkResponse> saveWifiNetwork(Spartan spartan, String ssid, @Nullable String key) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new SaveWifiNetworkQuery(suuntoBtDevice.getMacAddress(), ssid, key),
            SaveWifiNetworkResponse.class
        );
    }

    public Completable forgetWifiNetwork(Spartan spartan, int index) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new ForgetWifiNetworkQuery(suuntoBtDevice.getMacAddress(), index),
            EmptyResponse.class
        ).toCompletable();
    }

    public Single<ScanAvailableWifiNetworksResponse> scanAvailableWifiNetworks(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new ScanAvailableWifiNetworksQuery(suuntoBtDevice.getMacAddress()),
            ScanAvailableWifiNetworksResponse.class
        );
    }

    public Completable setWifiGeneralSettings(Spartan spartan, @NonNull String countryCode) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new SetWifiGeneralSettingsQuery(suuntoBtDevice.getMacAddress(), countryCode),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable setOfflineMapsUrl(Spartan spartan, @NonNull String url) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new SetOfflineMapsUrlQuery(suuntoBtDevice.getMacAddress(), url),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable setAuthToken(Spartan spartan, @NonNull String token) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new SetAuthTokenQuery(suuntoBtDevice.getMacAddress(), token),
            EmptyResponse.class
        ).toCompletable();
    }

    public Observable<Boolean> observeWifiEnabled(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendObservableQueryWhenServiceReady(
            new ObserveWifiEnabledQuery(suuntoBtDevice.getMacAddress()),
            ObserveWifiEnabledResponse.class
        ).map(ObserveWifiEnabledResponse::getEnabled);
    }

    public Completable setWifiEnabled(Spartan spartan, @NonNull Boolean enabled) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new SetWifiEnabledQuery(suuntoBtDevice.getMacAddress(), enabled),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable enableInboxWifi(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new EnableInboxWifiQuery(suuntoBtDevice.getMacAddress()),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable notifyAreaSelectionChanged(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new NotifyAreaSelectionChangedQuery(suuntoBtDevice.getMacAddress()),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable notifyAreaUnderDownloadDeleted(Spartan spartan, @NonNull String areaId) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new NotifyAreaUnderDownloadDeletedQuery(suuntoBtDevice.getMacAddress(), areaId),
            EmptyResponse.class
        ).toCompletable();
    }

    @NonNull
    public Single<Integer> numberOfAreas(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new NumberOfAreasQuery(suuntoBtDevice.getMacAddress()),
            NumberOfAreasResponse.class
        ).map(NumberOfAreasResponse::getNumberOfAreas);
    }

    public Observable<BatteryLevel> getBatteryLevel(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendObservableQueryWhenServiceReady(
            new GetBatteryLevelQuery(suuntoBtDevice.getMacAddress()),
            GetBatteryLevelResponse.class
        ).map(GetBatteryLevelResponse::getBatteryLevel);
    }

    public Single<ChargingState> getChargingState(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new GetChargingStateQuery(suuntoBtDevice.getMacAddress()),
            GetChargingStateResponse.class
        ).map(GetChargingStateResponse::getChargingState);
    }

    public Single<UsbCableState> getUsbCableState(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new GetUsbCableStateQuery(suuntoBtDevice.getMacAddress()),
            GetUsbCableStateResponse.class
        ).map(GetUsbCableStateResponse::getUsbCableState);
    }

    public Observable<UsbCableState> getUsbCableStateObservable(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendObservableQueryWhenServiceReady(
            new GetUsbCableStateQuery(suuntoBtDevice.getMacAddress()),
            GetUsbCableStateResponse.class
        ).map(GetUsbCableStateResponse::getUsbCableState);
    }

    public File getUserSettingsToWatchFile() {
        return new File(appContext.getFilesDir(), configuration.getUserSettingsToWatchPath());
    }

    public Single<File> getWUIDumpFile(Spartan spartan) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        String wuiDumpFileName = "wui_dump.bin";
        File wuiDumpFile = new File(appContext.getFilesDir(), configuration.getWatchFilePath(macAddress, wuiDumpFileName));
        return getFile(spartan, wuiDumpFileName, wuiDumpFile.getPath())
            .andThen(Single.just(wuiDumpFile));
    }

    public Completable lockSportsApp(
        @NonNull Spartan spartan,
        @NonNull String pluginId
    ) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        return sendQueryWhenServiceReady(
            new LockOrUnlockSportsAppQuery(macAddress, pluginId, true),
            EmptyResponse.class
        ).toCompletable();
    }

    public Completable unlockSportsApp(
        @NonNull Spartan spartan,
        @NonNull String pluginId
    ) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        return sendQueryWhenServiceReady(
            new LockOrUnlockSportsAppQuery(macAddress, pluginId, false),
            EmptyResponse.class
        ).toCompletable();
    }

    public Single<String> getZappPluginDirectory(
        @NonNull Spartan spartan
    ) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        return sendQueryWhenServiceReady(
            new GetZappPluginDirectoryQuery(macAddress),
            GetZappPluginDirectoryResponse.class
        ).map(response -> response.getZappPluginDirectory());
    }

    public Completable setNotificationEnabled(Spartan spartan, boolean enabled) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        return sendQueryWhenServiceReady(
            new PutNotificationEnabledQuery(macAddress, enabled),
            PutNotificationEnabledResponse.class
        ).toCompletable();
    }

    public Single<Boolean> getNotificationEnabled(Spartan spartan) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        return sendQueryWhenServiceReady(
            new GetNotificationEnabledQuery(macAddress),
            GetNotificationEnabledResponse.class
        ).map(GetNotificationEnabledResponse::getEnabled);
    }

    public Completable setNotificationCategoryEnabled(
        Spartan spartan,
        boolean call,
        boolean sms,
        boolean application) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        return sendQueryWhenServiceReady(
            new PutNotificationCategoryEnabledQuery(macAddress, call, sms, application),
            PutNotificationCategoryEnabledResponse.class
        ).toCompletable();
    }

    public Completable setDebugLocationCoordinates(Spartan spartan, int latitude, int longitude) {
        String macAddress = spartan.getSuuntoBtDevice().getMacAddress();
        return sendQueryWhenServiceReady(
            new DebugCommands.SetLocationCoordinatesQuery(macAddress, latitude, longitude),
            EmptyResponse.class
        ).toCompletable();
    }

    public Observable<SetupPreferenceInfo> subscribeSetupPreferenceCancel(Spartan spartan) {
        return sendObservableQueryWhenServiceReady(
            new SubscribeSetupPreferenceCancelQuery(spartan.getSuuntoBtDevice().getMacAddress()),
            SetupPreferenceInfo.class
        );
    }

    public Single<SetupPreferenceInfo> getSetupPreferenceState(Spartan spartan) {
        return sendQueryWhenServiceReady(
            new GetSetupPreferenceStateQuery(spartan.getSuuntoBtDevice().getMacAddress()),
            SetupPreferenceInfo.class
        );
    }

    public Completable setSetupPreferenceState(Spartan spartan, String userId, boolean start) {
        return sendQueryWhenServiceReady(
            new PutSetupPreferenceStateQuery(spartan.getSuuntoBtDevice().getMacAddress(), userId, start ? 2 : 3),
            EmptyResponse.class
        ).toCompletable();
    }

    public Observable<SetAppInfoResponse> setAppInfo(
        Spartan spartan,
        AppInfo appInfo
    ) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendObservableQueryWhenServiceReady(
            new SetAppInfoQuery(suuntoBtDevice.getMacAddress(), appInfo),
            SetAppInfoResponse.class
        );
    }

    public Completable setWatchWearDirection(
        Spartan spartan,
        WearDirection direction
    ) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new SetWearDirectionQuery(suuntoBtDevice.getMacAddress(), direction),
            SetupWearDirectionResponse.class
        ).toCompletable();
    }

    public Completable updateOtaManualDownloadFlag(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        return sendQueryWhenServiceReady(
            new UpdateOtaManualDownloadFlagQuery(suuntoBtDevice.getMacAddress()),
            EmptyResponse.class
        ).toCompletable();
    }

    public Single<List<File>> getLogFiles(Spartan spartan) {
        SuuntoBtDevice suuntoBtDevice = spartan.getSuuntoBtDevice();
        String macAddress = suuntoBtDevice.getMacAddress();
        return sendQueryWhenServiceReady(
            new GetLogFilesQuery(suuntoBtDevice.getMacAddress()),
            GetLogFilesResponse.class
        ).flatMap((Func1<GetLogFilesResponse, Single<List<File>>>) response ->
            Single.zip(
                response.getList().stream().map(fileName -> {
                    File logFile = new File(appContext.getFilesDir(), configuration.getWatchFilePath(macAddress, fileName));
                    return getFile(spartan, fileName, logFile.getPath()).andThen(Single.just(logFile));
                }).collect(Collectors.toList()),
                files -> Arrays.stream(files).map(file -> (File) file).collect(Collectors.toList())
            )
        );
    }

    /**
     * Handler of incoming messages from service.
     */
    private static class IncomingHandler extends Handler {
        private final ClassLoader classLoader;
        private final Subject<ResponseMessage, ResponseMessage> responsesSubject;
        private final Subject<Broadcast, Broadcast> broadcastsSubject;
        private final List<SuuntoQueryConsumer> consumers;

        IncomingHandler(Looper looper, ClassLoader classLoader,
                        Subject<ResponseMessage, ResponseMessage> responsesSubject,
                        Subject<Broadcast, Broadcast> broadcastsSubject,
                        List<SuuntoQueryConsumer> consumers) {
            super(looper);
            this.classLoader = classLoader;
            this.responsesSubject = responsesSubject;
            this.broadcastsSubject = broadcastsSubject;
            this.consumers = consumers;
        }

        @Override
        public void handleMessage(Message msg) {
            Timber.v("Received message from service: [" + msg + "]");
            Bundle bundle = msg.getData();
            bundle.setClassLoader(classLoader);
            Parcelable data = bundle.getParcelable(SuuntoRepositoryService.ArgumentKeys.ARG_DATA);
            switch (msg.what) {
                case SuuntoRepositoryService.MSG_RESPONSE:
                    if (!(data instanceof Response)) {
                        Timber.w("Invalid response data");
                    } else {
                        handleResponse(msg.arg1, (Response) data);
                    }
                    break;
                case SuuntoRepositoryService.MSG_BROADCAST:
                    if (!(data instanceof Broadcast)) {
                        Timber.w("Invalid broadcast message");
                    } else {
                        handleBroadcast((Broadcast) data);
                    }
                    break;
                default:
                    Timber.w("Unknown message type [" + msg.what + "]");
                    super.handleMessage(msg);
            }
        }

        private void handleBroadcast(Broadcast data) {
            Timber.v("handleBroadcast() called with: data = [" + data + "]");
            broadcastsSubject.onNext(data);
        }

        private void handleResponse(int messageId, @NonNull Response response) {
            ResponseMessage<? extends Response> responseMessage = null;
            Timber.v("handleResponse() called with: messageId = ["
                    + messageId
                    + "], data = ["
                    + response
                    + "]");
            // todo all this producers/consumers system is completely not needed, get rid of it
            for (SuuntoQueryConsumer suuntoQueryConsumer : consumers) {
                if (suuntoQueryConsumer.isResponseRelated(response)) {
                    responseMessage = suuntoQueryConsumer.getResponseMessage(messageId, response);
                }
            }
            if (responseMessage == null) {
                responseMessage = new ResponseMessage<>(messageId, response);
            }
            responsesSubject.onNext(responseMessage);
        }
    }
}
