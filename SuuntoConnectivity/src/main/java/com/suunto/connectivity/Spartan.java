package com.suunto.connectivity;

import android.net.Uri;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.suunto.connectivity.battery.BatteryLevel;
import com.suunto.connectivity.battery.ChargingState;
import com.suunto.connectivity.battery.UsbCableState;
import com.suunto.connectivity.logbook.Logbook;
import com.suunto.connectivity.repository.AppInfo;
import com.suunto.connectivity.repository.PairingState;
import com.suunto.connectivity.repository.SetAppInfoResponse;
import com.suunto.connectivity.repository.SuuntoRepositoryClient;
import com.suunto.connectivity.repository.commands.CheckForOtaUpdatesResponse;
import com.suunto.connectivity.repository.commands.FirmwareTransferStartResponse;
import com.suunto.connectivity.repository.commands.GetFileListResponse;
import com.suunto.connectivity.repository.commands.GetOrSetSettingsFileResponse;
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksCountResponse;
import com.suunto.connectivity.repository.commands.GetSavedWifiNetworksResponse;
import com.suunto.connectivity.repository.commands.GetSelectedFirmwareResponse;
import com.suunto.connectivity.repository.commands.GoalType;
import com.suunto.connectivity.repository.commands.InstallSelectedFirmwareResponse;
import com.suunto.connectivity.repository.commands.ResetConnectionResponse;
import com.suunto.connectivity.repository.commands.SaveWifiNetworkResponse;
import com.suunto.connectivity.repository.commands.ScanAvailableWifiNetworksResponse;
import com.suunto.connectivity.repository.commands.SelectFirmwareResponse;
import com.suunto.connectivity.repository.commands.ServiceStabilityResponse;
import com.suunto.connectivity.repository.commands.SetupPreferenceInfo;
import com.suunto.connectivity.repository.commands.SleepTrackingMode;
import com.suunto.connectivity.repository.commands.StopOtaUpdateResponse;
import com.suunto.connectivity.repository.commands.SyncTrainingZoneResponse;
import com.suunto.connectivity.settings.UnitSystem;
import com.suunto.connectivity.settings.WearDirection;
import com.suunto.connectivity.sportmodes.SportModesSpartanWrapper;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.watch.SpartanSyncResult;
import com.suunto.connectivity.watch.WatchState;
import java.io.File;
import java.time.DayOfWeek;
import java.util.List;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.Subscription;
import rx.functions.Action1;
import rx.schedulers.Schedulers;
import timber.log.Timber;

public class Spartan implements Logbook {
    private final SuuntoBtDevice suuntoBtDevice;
    private final SuuntoRepositoryClient suuntoRepositoryClient;
    private final Observable<WatchState> watchStateObservable;
    // TODO: where do we unsusbscribe ?
    private final Subscription watchStateSubscription;
    private PairingState scannedPairingState;
    private final SportModesSpartanWrapper sportModesSpartanWrapper;

    public Spartan(SuuntoBtDevice suuntoBtDevice, SuuntoRepositoryClient suuntoRepositoryClient,
        Observable<WatchState> watchStateObservable) {
        this.suuntoBtDevice = suuntoBtDevice;
        this.suuntoRepositoryClient = suuntoRepositoryClient;
        this.watchStateObservable = watchStateObservable;
        this.sportModesSpartanWrapper = new SportModesSpartanWrapper(this);

        watchStateSubscription = watchStateObservable.subscribeOn(Schedulers.computation())
            .subscribe(new Action1<WatchState>() {
                @Override
                public void call(WatchState watchState) {
                    Timber.v("Got new watch state [" + watchState + "] for watch [" + this + "]");
                }
            }, new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    Timber.w(throwable,
                        "Error while listening for watch [" + this + "] status updates");
                }
            });
        this.scannedPairingState = PairingState.Unknown;
    }

    public SuuntoRepositoryClient getSuuntoRepositoryClient() {
        return suuntoRepositoryClient;
    }

    public SportModesSpartanWrapper getSportModesSpartanWrapper() {
        return sportModesSpartanWrapper;
    }

    @Override
    public Single<List<Entry>> getLogbookEntries() {
        return suuntoRepositoryClient.getLatestLogbook(this);
    }

    /**
     * @return {@link Single} that emits success if the connection to the watch is established.
     * Emitting false doesn't mean it failed, the connection might happen later on asynchronously.
     */
    public Single<Boolean> connect() {
        return suuntoRepositoryClient.connect(this);
    }

    public Single<Boolean> disconnect() {
        return suuntoRepositoryClient.disconnect(this);
    }

    public Single<Boolean> unpair() {
        return suuntoRepositoryClient.unpair(this);
    }

    public Single<SpartanSyncResult> synchronize(boolean isActivityDataOnly) {
        return suuntoRepositoryClient.synchronize(this, isActivityDataOnly);
    }

    public Single<SyncTrainingZoneResponse> requestTrainingZoneSync() {
        return suuntoRepositoryClient.requestTrainingZoneSync(this);
    }

    public SuuntoBtDevice getSuuntoBtDevice() {
        return suuntoBtDevice;
    }

    public String getSerial() {
        return suuntoBtDevice.getSerial();
    }

    public Observable<WatchState> getStateChangeObservable() {
        return watchStateObservable;
    }

    public Single<String> getGNSSVersion() {
        return suuntoRepositoryClient.getLatestGnssVersion(this);
    }

    public Single<SpartanUserSettings> getSettings() {
        return suuntoRepositoryClient.getUserSettings(getSuuntoBtDevice().getMacAddress());
    }

    public Single<Boolean> getAdaptiveCoachEnabled() {
        return suuntoRepositoryClient.getCoachEnabled(this);
    }

    public Completable setAdaptiveCoachEnabled(boolean enabled) {
        return suuntoRepositoryClient.setCoachEnabled(this, enabled);
    }

    public Single<Integer> getStepsTarget() {
        return suuntoRepositoryClient.get247Target(this, GoalType.STEPS);
    }

    public Completable setStepsTarget(int stepsTarget) {
        return suuntoRepositoryClient.set247Target(this, stepsTarget, GoalType.STEPS);
    }

    public Single<Integer> getEnergyTarget() {
        return suuntoRepositoryClient.get247Target(this, GoalType.ENERGY);
    }

    public Completable setEnergyTarget(int energyTarget) {
        return suuntoRepositoryClient.set247Target(this, energyTarget, GoalType.ENERGY);
    }

    public Single<Integer> getSleepTarget() {
        return suuntoRepositoryClient.get247Target(this, GoalType.SLEEP);
    }

    public Completable setSleepTarget(int targetValue) {
        return suuntoRepositoryClient.set247Target(this, targetValue, GoalType.SLEEP);
    }

    public Single<Integer> getCurrentSteps() {
        return suuntoRepositoryClient.getCurrentDailyActivityDataValue(this, GoalType.STEPS);
    }

    public Single<Integer> getActiveEnergy() {
        return suuntoRepositoryClient.getCurrentDailyActivityDataValue(this, GoalType.ENERGY);
    }

    public Single<Integer> getMetabolicEnergy() {
        return suuntoRepositoryClient.getCurrentDailyActivityDataValue(this, GoalType.METABOLIC_ENERGY);
    }

    public Single<Integer> getCurrentBalance() {
        return suuntoRepositoryClient.getCurrentDailyActivityDataValue(this, GoalType.BALANCE);
    }

    public Single<Integer> getCurrentStressState() {
        return suuntoRepositoryClient.getCurrentDailyActivityDataValue(this, GoalType.STRESS_STATE);
    }

    public Single<Integer> getBedtimeStart() {
        return suuntoRepositoryClient.get247Target(this, GoalType.BEDTIME_START);
    }

    public Completable setBedtimeStart(int targetValue) {
        return suuntoRepositoryClient.set247Target(this, targetValue, GoalType.BEDTIME_START);
    }

    public Single<Integer> getBedtimeEnd() {
        return suuntoRepositoryClient.get247Target(this, GoalType.BEDTIME_END);
    }

    public Completable setBedtimeEnd(int targetValue) {
        return suuntoRepositoryClient.set247Target(this, targetValue, GoalType.BEDTIME_END);
    }

    public Single<SleepTrackingMode> getSleepTrackingMode() {
        return suuntoRepositoryClient.getSleepTrackingMode(this);
    }

    @NonNull
    public Completable setSleepTrackingMode(@NonNull SleepTrackingMode sleepTrackingMode) {
        return suuntoRepositoryClient.setSleepTrackingMode(this, sleepTrackingMode);
    }

    @NonNull
    public Single<Boolean> isSpO2NightlyEnabled() {
        return suuntoRepositoryClient.isSpO2NightlyEnabled(this);
    }

    @NonNull
    public Completable setSpO2NightlyEnabled(boolean enabled) {
        return suuntoRepositoryClient.setSpO2NightlyEnabled(this, enabled);
    }

    @NonNull
    public Single<Boolean> isHrvEnabled() {
        return suuntoRepositoryClient.isHrvEnabled(this);
    }

    @NonNull
    public Completable setHrvEnabled(boolean enabled) {
        return suuntoRepositoryClient.setHrvEnabled(this, enabled);
    }

    @NonNull
    public Single<Boolean> is247HrEnabled() {
        return suuntoRepositoryClient.is247HrEnabled(this);
    }

    @NonNull
    public Completable set247HrEnabled(boolean enabled) {
        return suuntoRepositoryClient.set247HrEnabled(this, enabled);
    }

    public Single<Float> getWeeklyTargetDuration() {
        return suuntoRepositoryClient.getWeeklyTargetDuration(this);
    }

    public Completable setWeeklyTargetDuration(float duration) {
        return suuntoRepositoryClient.setWeeklyTargetDuration(this, duration);
    }

    public Single<SpartanSyncResult> getLatestSyncResult() {
        return suuntoRepositoryClient.getLatestSyncResult(getSuuntoBtDevice().getMacAddress());
    }

    public Completable clearConnectionInstability() {
        return suuntoRepositoryClient.clearConnectionInstability();
    }

    public PairingState getScannedPairingState() {
        return scannedPairingState;
    }

    public void setScannedPairingState(PairingState scannedPairingState) {
        this.scannedPairingState = scannedPairingState;
    }

    public Completable markAsSynced(List<Long> entryIds) {
        return suuntoRepositoryClient.markAsSynced(getSuuntoBtDevice().getMacAddress(), entryIds);
    }

    public Single<ResetConnectionResponse> resetConnection(@Nullable Integer timeoutBeforeReconnect) {
        return suuntoRepositoryClient.resetConnection(this, timeoutBeforeReconnect);
    }

    public Single<ServiceStabilityResponse> getServiceStability() {
        return suuntoRepositoryClient.getServiceStability();
    }

    public Completable userLogout() {
        return suuntoRepositoryClient.userLogout();
    }

    public Single<FirmwareTransferStartResponse> startFirmwareTransfer(
        Uri fileUri,
        @Nullable String firmwareVersion) {
        return suuntoRepositoryClient.requestFirmwareTransferStart(getSuuntoBtDevice(), fileUri,
            firmwareVersion);
    }

    public Single<SelectFirmwareResponse> selectFirmware(long packageId, boolean forceUpdate) {
        return suuntoRepositoryClient.requestSelectFirmware(
            getSuuntoBtDevice(), packageId, forceUpdate
        );
    }

    public Single<GetSelectedFirmwareResponse> getSelectedFirmware() {
        return suuntoRepositoryClient.getSelectedFirmware(getSuuntoBtDevice());
    }

    public Single<CheckForOtaUpdatesResponse> checkForOtaUpdates(@Nullable Uri deepLinkUri) {
        return suuntoRepositoryClient.checkForOtaUpdates(this, deepLinkUri);
    }

    public Single<StopOtaUpdateResponse> stopOtaUpdate() {
        return suuntoRepositoryClient.stopOtaUpdate(this);
    }

    public Single<InstallSelectedFirmwareResponse> installSelectedFirmware(
        long packageId,
        boolean forceUpdate
    ) {
        return suuntoRepositoryClient.installSelectedFirmware(getSuuntoBtDevice(), packageId, forceUpdate);
    }

    public Single<GetOrSetSettingsFileResponse> getSettingsFile(
        Uri fileUri) {
        return suuntoRepositoryClient.getSettingsFile(this, fileUri);
    }

    public Single<GetOrSetSettingsFileResponse> setSettingsFile(
        Uri fileUri) {
        return suuntoRepositoryClient.setSettingsFile(this, fileUri);
    }

    public Completable setPredefinedReplies(List<String> predefinedReplies) {
        return suuntoRepositoryClient.setPredefinedReplies(this, predefinedReplies);
    }

    public Completable setFirstDayOfTheWeek(DayOfWeek firstDayOfTheWeek) {
        return suuntoRepositoryClient.setFirstDayOfTheWeek(this, firstDayOfTheWeek);
    }

    public Completable setUserGender(String gender) {
        return suuntoRepositoryClient.setUserGender(this, gender);
    }

    public Completable setUserHeight(float height) {
        return suuntoRepositoryClient.setUserHeight(this, height);
    }

    public Completable setUserWeight(float weight) {
        return suuntoRepositoryClient.setUserWeight(this, weight);
    }

    public Completable setUserBirthYear(int birthYear) {
        return suuntoRepositoryClient.setUserBirthYear(this, birthYear);
    }

    public Completable setUserMaxHeart(int maxHR) {
        return suuntoRepositoryClient.setUserMaxHR(this, maxHR);
    }

    public Completable setUserRestHeart(int restHR) {
        return suuntoRepositoryClient.setUserRestHR(this, restHR);
    }

    public Completable setUserUnitSystem(UnitSystem unitSystem) {
        return suuntoRepositoryClient.setUserUnitSystem(this, unitSystem);
    }

    public Single<GetFileListResponse> getFileList(String path) {
        return suuntoRepositoryClient.getFileList(this, path);
    }

    public Completable getFile(String deviceFilePath, String localFilePath) {
        return suuntoRepositoryClient.getFile(this, deviceFilePath, localFilePath);
    }

    public Completable putFile(String localFilePath, String deviceFilePath) {
        return suuntoRepositoryClient.putFile(this, localFilePath, deviceFilePath);
    }

    public Completable deleteFile(String deviceFilePath) {
        return suuntoRepositoryClient.deleteFile(this, deviceFilePath);
    }

    public Observable<GetSavedWifiNetworksCountResponse> getSavedWifiNetworksCount() {
        return suuntoRepositoryClient.getSavedWifiNetworksCount(this);
    }

    public Single<GetSavedWifiNetworksResponse> getSavedWifiNetworks() {
        return suuntoRepositoryClient.getSavedWifiNetworks(this);
    }

    public Single<SaveWifiNetworkResponse> saveWifiNetwork(String ssid, @Nullable String key) {
        return suuntoRepositoryClient.saveWifiNetwork(this, ssid, key);
    }

    public Completable forgetWifiNetwork(int index) {
        return suuntoRepositoryClient.forgetWifiNetwork(this, index);
    }

    public Single<ScanAvailableWifiNetworksResponse> scanAvailableWifiNetworks() {
        return suuntoRepositoryClient.scanAvailableWifiNetworks(this);
    }

    public Completable setWifiGeneralSettings(@NonNull String countryCode) {
        return suuntoRepositoryClient.setWifiGeneralSettings(this, countryCode);
    }

    public Completable setOfflineMapsUrl(@NonNull String url) {
        return suuntoRepositoryClient.setOfflineMapsUrl(this, url);
    }

    public Completable setAuthToken(@NonNull String token) {
        return suuntoRepositoryClient.setAuthToken(this, token);
    }

    public Observable<Boolean> observeWifiEnabled() {
        return suuntoRepositoryClient.observeWifiEnabled(this);
    }

    public Completable setWifiEnabled(boolean enabled) {
        return suuntoRepositoryClient.setWifiEnabled(this, enabled);
    }

    public Completable enableInboxWifi() {
        return suuntoRepositoryClient.enableInboxWifi(this);
    }

    public Completable notifyAreaSelectionChanged() {
        return suuntoRepositoryClient.notifyAreaSelectionChanged(this);
    }

    public Completable notifyAreaUnderDownloadDeleted(@NonNull String areaId) {
        return suuntoRepositoryClient.notifyAreaUnderDownloadDeleted(this, areaId);
    }

    @NonNull
    public Single<Integer> numberOfAreas() {
        return suuntoRepositoryClient.numberOfAreas(this);
    }

    public Observable<BatteryLevel> getBatteryLevel() {
        return suuntoRepositoryClient.getBatteryLevel(this);
    }

    public Single<ChargingState> getChargingState() {
        return suuntoRepositoryClient.getChargingState(this);
    }

    public Single<UsbCableState> getUsbCableState() {
        return suuntoRepositoryClient.getUsbCableState(this);
    }

    public Observable<UsbCableState> getUsbCableStateObservable() {
        return suuntoRepositoryClient.getUsbCableStateObservable(this);
    }

    public Single<File> getWUIDumpFile() {
        return suuntoRepositoryClient.getWUIDumpFile(this);
    }

    public Completable lockSportsAppSettings(@NonNull String pluginId) {
        return suuntoRepositoryClient.lockSportsApp(this, pluginId);
    }

    public Completable unlockSportsAppSettings(@NonNull String pluginId) {
        return suuntoRepositoryClient.unlockSportsApp(this, pluginId);
    }

    public Single<String> getZappPluginDirectory() {
        return suuntoRepositoryClient.getZappPluginDirectory(this);
    }

    public Completable setNotificationEnabled(boolean enabled) {
        return suuntoRepositoryClient.setNotificationEnabled(this, enabled);
    }

    public Completable setNotificationCategoryEnabled(
        boolean call,
        boolean sms,
        boolean application) {
        return suuntoRepositoryClient.setNotificationCategoryEnabled(this, call, sms, application);
    }

    public Single<Boolean> getNotificationEnabled() {
        return suuntoRepositoryClient.getNotificationEnabled(this);
    }

    public Completable setDebugLocationCoordinates(int latitude, int longitude) {
        return suuntoRepositoryClient.setDebugLocationCoordinates(this, latitude, longitude);
    }

    public Observable<SetupPreferenceInfo> subscribeSetupPreferenceCancel() {
        return suuntoRepositoryClient.subscribeSetupPreferenceCancel(this);
    }

    public Single<SetupPreferenceInfo> getSetupPreferenceState() {
        return suuntoRepositoryClient.getSetupPreferenceState(this);
    }

    public Completable setSetupPreferenceState(String userId, boolean start) {
        return suuntoRepositoryClient.setSetupPreferenceState(this, userId, start);
    }

    public Observable<SetAppInfoResponse> setAppInfo(AppInfo appInfo) {
        return suuntoRepositoryClient.setAppInfo(this, appInfo);
    }

    public Completable setWatchWearDirection(WearDirection direction) {
        return suuntoRepositoryClient.setWatchWearDirection(this, direction);
    }

    public Completable updateOtaManualDownloadFlag() {
        return suuntoRepositoryClient.updateOtaManualDownloadFlag(this);
    }

    public Single<List<File>> getLogFiles() {
        return suuntoRepositoryClient.getLogFiles(this);
    }
}
