package com.stt.android.domain.device

import android.content.Context
import android.os.Build

import com.stt.android.domain.BaseUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.logging.TimberInMemoryTree
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

/**
 * Use case responsible for loading logs with given ids from the device and
 * sending them to the backend
 */
class SendDeviceLogsUseCase @Inject constructor(
    private val repository: DeviceLogRepository,
    private val guideLogCollector: SuuntoPlusGuideLogCollector,
    private val wuiDumpCollector: WUIDumpCollector,
    private val timberInMemoryTree: TimberInMemoryTree,
    private val watchLogFilesCollector: WatchLogFilesCollector,
) {
    /**
     * Collect logs and send them to backend or a local file
     *
     * @param filesDir Directory for temporary log storage (Context.getFilesDir())
     * @param logIds List of log types (see ScLib.LogType)
     * @param toFile If true, logs are sent to file only
     */
    suspend fun loadAndSendLogs(
        filesDir: File,
        logIds: List<Int>,
        toFile: Boolean,
        remoteLogId: String,
        context: Context
    ) = withContext(Dispatchers.IO) {
        val watchLogs = async { logIds.flatMap { repository.getLogs(it) } }

        val wuiDump = async {
            runSuspendCatching {
                listOf(wuiDumpCollector.getWUIDumpFile())
            }.getOrElse { e ->
                Timber.w(e, "Failed to get wuidump for log sending")
                emptyList()
            }
        }

        val watchLogFiles = async { watchLogFilesCollector.getWatchLogFiles() }

        val uiProcessTimberLogs = async {
            runSuspendCatching {
                val file = timberInMemoryTree.writeLogsToFile(filesDir.absolutePath, "ui-process")
                file?.let {
                    val metadata = getAppMetadata(context)
                    val original = it.readText()
                    it.writeText(metadata + original)
                }
                listOfNotNull(file)
            }.getOrElse { e ->
                Timber.w(e, "Failed to get UI logs for log sending")
                emptyList()
            }
        }

        val suuntoPlusGuideLogs = async {
            runSuspendCatching {
                guideLogCollector.getSuuntoPlusGuideLogs()
            }.getOrElse { e ->
                Timber.w(e, "Failed to get SuuntoPlus Guide for log sending")
                emptyList()
            }
        }

        val headsetLogs = async {
            runSuspendCatching {
                repository.getHeadsetLogs()
            }.getOrElse { e ->
                Timber.w(e, "Failed to get headset logs for log sending")
                emptyList()
            }
        }

        val logFiles = watchLogs.await() +
            wuiDump.await() +
            watchLogFiles.await() +
            suuntoPlusGuideLogs.await() +
            uiProcessTimberLogs.await() +
            headsetLogs.await()
        repository.sendLogs(logFiles, toFile, remoteLogId)
    }

    private fun getAppMetadata(context: Context) = try {
        val pm = context.packageManager
        val packageInfo = pm.getPackageInfo(context.packageName, 0)
        val locale = context.resources.configuration.locales[0]
        val timeZone = java.util.TimeZone.getDefault().id
        val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo.longVersionCode
        } else {
            // Suppress deprecation warning in older versions
            @Suppress("DEPRECATION")
            packageInfo.versionCode.toLong()
        }
        val androidVersion = Build.VERSION.RELEASE
        val deviceModel = "${Build.MANUFACTURER} ${Build.MODEL}"
        val installTimeMillis = packageInfo.firstInstallTime
        val installTimeFormatted = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            .format(Date(installTimeMillis))

        buildString {
            appendLine("Package: ${packageInfo.packageName}")
            appendLine("Version: ${packageInfo.versionName} ($versionCode)")
            appendLine("Locale: $locale")
            appendLine("TimeZone: $timeZone")
            appendLine("Device: $deviceModel")
            appendLine("Android: $androidVersion")
            appendLine("Installed: $installTimeFormatted")
            appendLine("")
        }
    } catch (e: Exception) {
        Timber.w(e, "Failed to retrieve log metadata")
        "Metadata info unavailable\n"
    }
}
