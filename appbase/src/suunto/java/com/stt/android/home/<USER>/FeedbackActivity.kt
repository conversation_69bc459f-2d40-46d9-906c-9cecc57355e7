package com.stt.android.home.feedback

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.addCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.databinding.ActivityFeedbackBinding
import com.stt.android.extensions.openAppSettings
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.remote.di.BaseUrlConfiguration
import com.stt.android.utils.PermissionUtils
import com.stt.android.watch.sendlog.SendLogState
import com.stt.android.watch.sendlog.SendLogViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class FeedbackActivity : AppCompatActivity() {

    private val feedbackViewModel: FeedbackViewModel by viewModels()
    private val sendLogViewModel: SendLogViewModel by viewModels()
    private lateinit var binding: ActivityFeedbackBinding
    private var sendingLogsSnackBar: Snackbar? = null

    @Inject
    lateinit var baseUrlConfiguration: BaseUrlConfiguration

    private val fileChooserLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            val resultData = if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let { arrayOf(it) }
            } else {
                null
            }
            pathCallback?.onReceiveValue(resultData)
            pathCallback = null
        }
    private val requestPermissionLauncher: ActivityResultLauncher<String> =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted == false && !shouldShowRational()) {
                openAppSettings()
                cancelPathCallback()
            } else if (isGranted) {
                openFileChooser()
            } else {
                cancelPathCallback()
            }
        }

    private var pathCallback: ValueCallback<Array<Uri>>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFeedbackBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initToolbar()
        initWebView()
        feedbackViewModel.feedbackDeviceInfoJsonEvent.observeNotNull(this) {
            setDeviceInfoJson(it)
        }
        feedbackViewModel.sessionKeyEvent.observeNotNull(this) {
            setSessionKeyJson(it)
        }
        launchOnLifecycle {
            sendLogViewModel.sendLogState.collect {
                Timber.d("send log state: $it")
                if (it is SendLogState.Sending) {
                    showSendLogSnackbar(it.watchConnected)
                } else {
                    dismissSendLogSnackbar()
                }

                when (it) {
                    is SendLogState.Fail -> handleFeedbackResult(false)
                    is SendLogState.Success -> handleFeedbackResult(true)
                    else -> {}
                }
            }
        }

        val feedbackUrl = buildString {
            append(baseUrlConfiguration.feedbackUrl)
            append(FEEDBACK_PATCH)
            append("${Locale.getDefault().language}-${Locale.getDefault().country}")
        }
        binding.webView.loadUrl(feedbackUrl)
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebView() {
        binding.webView.addJavascriptInterface(this, "js_android")
        binding.webView.settings.cacheMode = WebSettings.LOAD_NO_CACHE
        binding.webView.settings.domStorageEnabled = true
        binding.webView.settings.javaScriptEnabled = true
        binding.webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (isDestroyed) return
                if (newProgress == 100) {
                    binding.progressBar.visibility = View.GONE
                }
            }

            override fun onShowFileChooser(
                webView: WebView?,
                filePathCallback: ValueCallback<Array<Uri>>,
                fileChooserParams: FileChooserParams?
            ): Boolean {
                pathCallback?.onReceiveValue(null)
                pathCallback = filePathCallback

                if (!EasyPermissions.hasPermissions(
                        this@FeedbackActivity,
                        *PermissionUtils.STORAGE_PERMISSIONS
                    )
                ) {
                    showRequestPermissionConfirmationDialog()
                } else {
                    openFileChooser()
                }
                return true
            }
        }
        binding.webView.webViewClient = object : WebViewClient() {
            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                if (isDestroyed) return
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun openFileChooser() {
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            putExtra(Intent.EXTRA_MIME_TYPES, MediaStoreUtils.SUPPORTED_IMAGE_MIME_TYPES)
            type = "*/*"
        }
        fileChooserLauncher.launch(
            Intent.createChooser(
                intent,
                getString(R.string.gallery_menu_launch_file_picker)
            )
        )
    }

    private fun setDeviceInfoJson(json: String) {
        val method = "javascript:$GET_DEVICE_INFO(\'$json\')"
        binding.webView.evaluateJavascript(method) {
            // do nothing
        }
    }

    private fun setSessionKeyJson(json: String) {
        val method = "javascript:$GET_ANNUAL_REPORT_USER_INFO(\'$json\')"
        binding.webView.evaluateJavascript(method) {
            // do nothing
        }
    }

    private fun handleFeedbackResult(success: Boolean) {
        val status = if (success) 1 else 0
        val method = "javascript:$HANDLE_FEEDBACK_RESULT($status)"
        binding.webView.evaluateJavascript(method) {
            // do nothing
        }
    }

    private fun shouldShowRational(): Boolean {
        return shouldShowRequestPermissionRationale(Manifest.permission.READ_EXTERNAL_STORAGE)
    }

    private fun showRequestPermissionConfirmationDialog() {
        AlertDialog.Builder(this)
            .setTitle(R.string.request_permission)
            .setMessage(R.string.request_storage_permission_purpose)
            .setPositiveButton(
                R.string.allow
            ) { _, _ ->
                requestPermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            .setNegativeButton(
                R.string.cancel
            ) { _, _ ->
                cancelPathCallback()
            }
            .show()
    }

    private fun cancelPathCallback() {
        pathCallback?.onReceiveValue(null)
        pathCallback = null
    }

    @JavascriptInterface
    fun js_annualReport(param: String) {
        Timber.d("js_annualReport $param")
        if (param == BACK_METHOD_NAME) {
            finish()
        } else if (param == GET_DEVICE_INFO) {
            feedbackViewModel.getDeviceInfo(this)
        }
    }

    @JavascriptInterface
    fun js_annualReport() {
        // js get the sessionKey
        feedbackViewModel.getSessionKey()
    }

    @JavascriptInterface
    fun js_setLogId(remoteLogId: String) {
        sendLogViewModel.sendLogs(this, remoteLogId)
    }

    private fun initToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = getString(R.string.feedback)
        binding.toolbar.setNavigationOnClickListener {
            if (isSendingLogs()) {
                showExitDialog()
            } else {
                finish()
            }
        }
        onBackPressedDispatcher.addCallback(this) {
            if (!isSendingLogs()) {
                finish()
            }
        }
    }

    private fun isSendingLogs(): Boolean = sendLogViewModel.isSendingLog()

    private fun showExitDialog() {
        AlertDialog.Builder(this)
            .setTitle(R.string.assessment_attention)
            .setMessage(R.string.feedback_exit_tips)
            .setPositiveButton(R.string.exit) { _, _ ->
                lifecycleScope.launch {
                    sendLogViewModel.cancelSendingLog()
                    finish()
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    private fun showSendLogSnackbar(watchConnected: Boolean) {
        if (sendingLogsSnackBar == null) {
            val snackBar = Snackbar.make(
                binding.root,
                if (watchConnected)
                    R.string.watch_snackbar_sending_logs
                else
                    R.string.watch_snackbar_sending_logs_with_no_watch,
                Snackbar.LENGTH_INDEFINITE
            )
            snackBar.show()
            sendingLogsSnackBar = snackBar
        }
    }

    private fun dismissSendLogSnackbar() {
        sendingLogsSnackBar?.dismiss()
        sendingLogsSnackBar = null
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissSendLogSnackbar()
    }

    companion object {
        private const val BACK_METHOD_NAME = "goBackApp"
        private const val GET_DEVICE_INFO = "getDeviceInfo"
        private const val GET_ANNUAL_REPORT_USER_INFO = "getAnnualReportUserInfo"
        private const val HANDLE_FEEDBACK_RESULT = "handleFeedbackResult"

        private const val FEEDBACK_PATCH = "feedback/user-feedback?lang="

        @JvmStatic
        fun newIntent(context: Context) = Intent(context, FeedbackActivity::class.java)
    }
}
