package com.stt.android.watch.sendlog

import android.content.Context
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.domain.device.SendDeviceLogsUseCase
import com.stt.android.watch.DeviceActionEvent
import com.stt.android.watch.LogSendEnded
import com.stt.android.watch.LogSendStarted
import com.suunto.connectivity.ScLib
import io.reactivex.Completable
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.rx2.rxCompletable
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SendLogHelper @Inject constructor(
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    private val deviceLogUseCase: SendDeviceLogsUseCase,
    private val context: Context,
) {

    fun sendLogs(
        filesDir: File,
        toFile: Boolean,
        remoteLogId: String = "",
        onDeviceAction: (event: DeviceActionEvent) -> Unit,
    ): Disposable {
        val logIds = listOf(
            ScLib.LOG_MDS,
            ScLib.LOG_WATCH_SYSTEM_EVENTS,
            ScLib.LOG_REPOSITORY_SERVICE,
            ScLib.LOG_AMBIT3_MOVE_BINS,
            ScLib.LOG_MDS_DESCRIPTORS
        )
        return deviceConnectionStateUseCase
            .connectedWatchState()
            .firstOrError()
            .map { connectedWatchState ->
                connectedWatchState.connectedWatchConnectionState == ConnectedWatchConnectionState.CONNECTED
            }
            .onErrorReturn { false }
            .doOnSuccess { connected ->
                Timber.w("Gathering logs for sending, watch connected: $connected")
                onDeviceAction(LogSendStarted(watchConnected = connected))
            }
            .ignoreElement()
            .andThen(rxCompletable { deviceLogUseCase.loadAndSendLogs(filesDir, logIds, toFile, remoteLogId, context) })
            .mergeWith(Completable.timer(2, TimeUnit.SECONDS))
            .doOnError {
                onDeviceAction(LogSendEnded(it))
            }
            .doOnComplete {
                onDeviceAction(LogSendEnded(null))
            }
            .subscribe(
                { Timber.d("Sending logs completed") },
                { throwable -> Timber.w(throwable, "Failed to send logs") }
            )
    }
}

sealed class SendLogState {
    data object Idle : SendLogState()
    data class Sending(val remoteLogId: String, val watchConnected: Boolean) : SendLogState()
    data class Success(val remoteLogId: String) : SendLogState()
    data class Fail(val remoteLogId: String, val throwable: Throwable) : SendLogState()
}
