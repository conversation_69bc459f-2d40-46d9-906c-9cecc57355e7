package com.stt.android.watch.sendlog

import android.content.Context
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.remote.feedback.LogStatus
import com.stt.android.watch.LogSendEnded
import com.stt.android.watch.LogSendStarted
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SendLogViewModel @Inject constructor(
    private val sendLogHelper: SendLogHelper,
    private val sendLogDataSource: SendLogDataSource,
    dispatchers: CoroutinesDispatchers
) : CoroutineViewModel(dispatchers) {
    private val _sendLogState = MutableStateFlow<SendLogState>(SendLogState.Idle)
    val sendLogState: StateFlow<SendLogState> = _sendLogState.asStateFlow()

    private var sendLogDisposable: Disposable? = null

    fun sendLogs(context: Context, remoteLogId: String = "") {
        Timber.d("send logs(id=$remoteLogId) start")
        sendLogDisposable = sendLogHelper.sendLogs(context.filesDir, false, remoteLogId) { event ->
            Timber.d("send logs(id=$remoteLogId), event: $event")
            when (event) {
                is LogSendStarted -> _sendLogState.value =
                    SendLogState.Sending(remoteLogId, event.watchConnected)

                is LogSendEnded -> {
                    _sendLogState.value =
                        event.throwable?.let {
                            updateRemoteLogStatus(remoteLogId, LOG_STATUS_FAILED)
                            SendLogState.Fail(remoteLogId, it)
                        } ?: run {
                            updateRemoteLogStatus(remoteLogId, LOG_STATUS_SUCCESS)
                            SendLogState.Success(remoteLogId)
                        }
                }

                else -> {}
            }
        }
    }

    fun isSendingLog(): Boolean {
        return _sendLogState.value is SendLogState.Sending
    }

    fun cancelSendingLog() {
        (_sendLogState.value as? SendLogState.Sending)?.apply {
            updateRemoteLogStatus(remoteLogId, LOG_STATUS_INTERRUPTED)
        }
    }

    private fun updateRemoteLogStatus(remoteLogId: String, status: String) {
        if (remoteLogId.isEmpty()) return
        launch(io + NonCancellable) {
            runSuspendCatching {
                sendLogDataSource.updateLogStatus(LogStatus(id = remoteLogId, status = status))
            }.onFailure {
                Timber.w(it, "updateRemoteLogStatus error")
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        clearDisposable()
    }

    private fun clearDisposable() {
        sendLogDisposable?.dispose()
        sendLogDisposable = null
    }

    companion object {
        private const val LOG_STATUS_SUCCESS = "SUCCESS"
        private const val LOG_STATUS_FAILED = "FAILED"
        private const val LOG_STATUS_INTERRUPTED = "INTERRUPTED"
    }
}
