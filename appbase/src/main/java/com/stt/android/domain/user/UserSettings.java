package com.stt.android.domain.user;

import android.content.Context;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.R;
import com.stt.android.data.usersettings.MenstrualCycleSettings;
import static com.stt.android.domain.user.MeasurementUnitKt.DEFAULT_MEASUREMENT_UNIT;
import static com.stt.android.domain.user.MeasurementUnitKt.getMeasurementUnitByCountryIso;
import com.stt.android.maps.MapType;
import com.stt.android.maps.MapTypeHelper;
import static com.stt.android.maps.SuuntoMapTypesKt.MAP_TYPE_DEFAULT;
import com.stt.android.settings.UserSettingsProperty;
import com.stt.android.sharedpreference.mapping.SharedPreference;
import com.stt.android.utils.DateUtils;
import com.stt.android.utils.DeviceUtils;
import com.stt.android.utils.LocaleUtils;
import java.time.DayOfWeek;
import java.time.temporal.WeekFields;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

/**
 * <code>storeAsString</code> needs to be true for the values that are editable
 * through preferences, because the preferences will store them as {@link String}s anyway.
 */
public class UserSettings implements LocallyChangeable {
    /**
     * Key used to store {@link #locallyChanged} value in Shared Preferences
     */
    public static final String LOCALLY_CHANGED_KEY = "user_settings_locally_changed";

    public static final String HR_MAXIMUM_KEY = "heart_rate_maximum";
    public static final String HR_REST_KEY = "heart_rate_rest";
    public static final String HR_ZONES_TYPE = "hr_zones_type";
    public static final String COMBINED_ZONES = "combined_zones_v2";

    public static final String WHEEL_CIRCUMFERENCE = "wheel_circumference";
    public static final String CADENCE_DATA_SOURCE = "cadence_data_source";
    public static final String ALTITUDE_SOURCE = "altitude_source";
    public static final String GENDER = "gender";
    public static final String COUNTRY = "country";
    public static final String COUNTRY_SUBDIVISION = "countrySubdivision";
    public static final String BIRTH_DATE = "birth_date";
    public static final String PHONE_NUMBER = "phone_number";
    public static final String ALTITUDE_SOURCE_SET_BY_USER = "altitude_source_set_by_user";
    public static final String REAL_NAME = "realName";
    public static final String DESCRIPTION = "description";
    public static final String HEIGHT = "height";
    public static final String WEIGHT = "weight";
    public static final int DEFAULT_WEIGHT_GRAMS = 70000;
    public static final int DEFAULT_WEIGHT_KILOGRAMS = DEFAULT_WEIGHT_GRAMS / 1000;

    public static final String SELECTED_MAP_TYPE_KEY = "default_maptype";

    public static final int DEFAULT_WEIGHT_POUNDS = 100;
    /**
     * Default maximum heart rate value
     */
    public static final int DEFAULT_MAX_HR = 180;
    public static final int DEFAULT_REST_HR = 50;

    public static final int DEFAULT_AGE = 30;
    public static final long DEFAULT_BIRTH_DATE = DateUtils.calculateBirthDate(DEFAULT_AGE);
    public static final int DEFAULT_HEIGHT_CENTIMETERS = 170;
    public static final int DEFAULT_HEIGHT_FEET = 5;
    public static final int DEFAULT_HEIGHT_INCH = 7;
    public static final int DEFAULT_WHEEL_CIRCUMFERENCE = 1800;
    public static final boolean DEFAULT_KEEP_ABOVE_LOCK = true;
    public static final String DEFAULT_USER_ANALYTICS_UUID = "";
    public static final String FIRST_DAY_OF_THE_WEEK_KEY = "firstDayOfTheWeek";

    public static final String DEFAULT_USER_SETTINGS_SHARE_PRIVACY = "0";

    /**
     * Shared preference used to keep track of user settings being saved for the first time or not.
     */
    public static final String USER_SETTINGS_SAVED_KEY = "USER_SETTINGS_SAVED";

    public static final String DEFAULT_MENSTRUAL_CYCLE_LENGTH = "-1";
    public static final String DEFAULT_MENSTRUAL_PERIOD_DURATION = "-1";

    /**
     * UTC Timestamp at which these settings were saved
     */
    public static final String USER_SETTINGS_LAST_SAVED_TIMESTAMP_KEY =
        "USER_SETTINGS_LAST_SAVED_TIMESTAMP";

    /**
     * UTC Timestamp when user profile changed and saved
     */
    public static final String USER_SETTINGS_LAST_AGE_SAVED_TIMESTAMP_KEY =
        "last_age_save_timestamp";
    public static final String USER_SETTINGS_LAST_GENDER_SAVED_TIMESTAMP_KEY =
        "last_gender_save_timestamp";
    public static final String USER_SETTINGS_LAST_WEIGHT_SAVED_TIMESTAMP_KEY =
        "last_weight_save_timestamp";
    public static final String USER_SETTINGS_LAST_HEIGHT_SAVED_TIMESTAMP_KEY =
        "last_height_save_timestamp";
    public static final String USER_SETTINGS_LAST_MAX_HR_SAVED_TIMESTAMP_KEY =
        "last_max_hr_save_timestamp";
    public static final String USER_SETTINGS_LAST_REST_HR_SAVED_TIMESTAMP_KEY =
        "last_rest_hr_save_timestamp";

    // Account settings
    @SharedPreference(value = "country", defaultValue = "")
    private final String country;
    @SharedPreference(value = "countrySubdivision", defaultValue = "")
    private final String countrySubdivision;
    @SharedPreference(value = "language", defaultValue = "")
    private final String language;

    // General settings
    @SharedPreference(value = "measurement_unit", defaultValue = "METRIC")
    private final MeasurementUnit measurementUnit;

    @SharedPreference(value = HR_MAXIMUM_KEY, storeAsString = true, defaultValue = "180")
    private final int hrMaximum;

    // Cadence settings
    @SharedPreference(value = WHEEL_CIRCUMFERENCE, storeAsString = true, defaultValue = "1800")
    private final int wheelCircumference;
    @SharedPreference(value = CADENCE_DATA_SOURCE, defaultValue = "CADENCE")
    private final CadenceDataSource cadenceDataSource;

    // User settings
    @SharedPreference(value = GENDER, defaultValue = "MALE")
    private final Sex gender;
    @SharedPreference(value = HEIGHT, storeAsString = true, defaultValue = "180")
    private final Integer height;
    @SharedPreference(value = WEIGHT, storeAsString = true, defaultValue = "70000")
    private final Integer weight;
    @SharedPreference(value = BIRTH_DATE, storeAsString = true, defaultValue = "")
    private final Long birthDate;
    @SharedPreference(value = "UUID", storeAsString = true, defaultValue = "")
    private final String analyticsUUID;

    @SharedPreference(value = "email", storeAsString = true, defaultValue = "")
    private final String email;

    @SharedPreference(value = "screen_backlight", defaultValue = "false")
    private final ScreenBacklightSetting screenBacklight;
    /**
     * If true, the workout activity will remain above the lock screen.
     */
    @SharedPreference(value = "keep_above_lock", storeAsString = true, defaultValue = "true")
    private final boolean keepAboveLock;
    @SharedPreference(value = "gps_filtering", storeAsString = true, defaultValue = "false")
    private final boolean gpsFiltering;
    @SharedPreference(value = "altitude_offset", storeAsString = true, defaultValue = "0.0")
    private final float altitudeOffset;

    @SharedPreference(value = SELECTED_MAP_TYPE_KEY, defaultValue = MAP_TYPE_DEFAULT)
    private final String selectedMapType;

    @SharedPreference(value = LOCALLY_CHANGED_KEY, defaultValue = "false")
    private final boolean locallyChanged;

    @SharedPreference(value = ALTITUDE_SOURCE, defaultValue = "GPS")
    private final AltitudeSource altitudeSource;

    @SharedPreference(value = ALTITUDE_SOURCE_SET_BY_USER, storeAsString = true, defaultValue =
        "false")
    private final boolean altitudeSourceSetByUser;

    @SharedPreference(value = USER_SETTINGS_LAST_AGE_SAVED_TIMESTAMP_KEY, defaultValue = "0")
    private final Long lastAgeSaveTimestamp;

    @SharedPreference(value = USER_SETTINGS_LAST_GENDER_SAVED_TIMESTAMP_KEY, defaultValue = "0")
    private final Long lastGenderSaveTimestamp;

    @SharedPreference(value = USER_SETTINGS_LAST_WEIGHT_SAVED_TIMESTAMP_KEY, defaultValue = "0")
    private final Long lastWeightSaveTimestamp;

    @SharedPreference(value = USER_SETTINGS_LAST_HEIGHT_SAVED_TIMESTAMP_KEY, defaultValue = "0")
    private final Long lastHeightSaveTimestamp;

    @SharedPreference(value = USER_SETTINGS_LAST_MAX_HR_SAVED_TIMESTAMP_KEY, defaultValue = "0")
    private final Long lastMaxHRSaveTimestamp;

    @SharedPreference(value = USER_SETTINGS_LAST_REST_HR_SAVED_TIMESTAMP_KEY, defaultValue = "0")
    private final Long lastRestHRSaveTimestamp;

    // notification settings
    @SharedPreference(value = "notification_sound_enabled", defaultValue = "true")
    private final boolean notificationSoundEnabled;

    @SharedPreference(value = "notification_push_workout_commented", defaultValue = "true")
    private final boolean notificationWorkoutCommentedPushEnabled;

    @SharedPreference(value = "notification_push_workout_shared", defaultValue = "true")
    private final boolean notificationWorkoutSharedPushEnabled;

    @SharedPreference(value = "notification_push_workout_reacted", defaultValue = "true")
    private final boolean notificationWorkoutReactedPushEnabled;

    @SharedPreference(value = "notification_push_facebook_friend_joined", defaultValue = "true")
    private final boolean notificationFacebookFriendJoinedPushEnabled;

    @SharedPreference(value = "notification_push_new_follower", defaultValue = "true")
    private final boolean notificationNewFollowerPushEnabled;

    @SharedPreference(value = "notification_new_activity_synced", defaultValue = "true")
    private final boolean notificationNewActivitySyncedLocalEnabled;

    @SharedPreference(value = "notification_email_workout_commented", defaultValue = "true")
    private final boolean notificationWorkoutCommentedEmailEnabled;

    @SharedPreference(value = "notification_email_new_follower", defaultValue = "true")
    private final boolean notificationNewFollowerEmailEnabled;

    @SharedPreference(value = "notification_email_workout_shared", defaultValue = "true")
    private final boolean notificationWorkoutSharedEmailEnabled;

    @SharedPreference(value = "notification_email_digest", defaultValue = "true")
    private final boolean notificationDigestEmailEnabled;

    @SharedPreference(value = "auto_approve_followers", defaultValue = "false")
    private final boolean autoApproveFollowersEnabled;

    @SharedPreference(value = "private_account", defaultValue = "false")
    private final boolean privateAccount;

    @SharedPreference(value = "showLocale", defaultValue = "false")
    private final boolean showLocale;

    // we use -1 as default value to differentiate if the value was read from backend or not
    @SharedPreference(value = "optinAccepted", defaultValue = "-1")
    private final long optinAccepted;

    @SharedPreference(value = "optinRejected", defaultValue = "-1")
    private final long optinRejected;

    @SharedPreference(value = "optinLastShown", defaultValue = "-1")
    private final long optinLastShown;

    @SharedPreference(value = "optinShowCount", defaultValue = "-1")
    private final long optinShowCount;

    @SharedPreference(value = "realName", storeAsString = true, defaultValue = "")
    private final String realName;

    @SharedPreference(value = "profileDescription", storeAsString = true, defaultValue = "")
    private final String description;

    @SharedPreference(value = "sharingFlagPreference", defaultValue =
        DEFAULT_USER_SETTINGS_SHARE_PRIVACY)
    private final int sharingFlagPreference;

    @SharedPreference(value = "hasOutboundPartnerConnections", defaultValue = "false")
    private final boolean hasOutboundPartnerConnections;

    @SharedPreference(value = PHONE_NUMBER, defaultValue = "")
    private final String phoneNumber;

    @SharedPreference(value = "predefinedReplies", defaultValue = "[]")
    private final String[] predefinedReplies;

    @SharedPreference(
        value = "preferredTssCalculationMethods",
        defaultValue = "{}",
        mapKeyType = Integer.class,
        mapValueType = String.class
    )
    private final Map<Integer, String> preferredTssCalculationMethods;

    @SharedPreference(value = "firstDayOfTheWeek", defaultValue = "MONDAY")
    private final DayOfWeek firstDayOfTheWeek;

    @SharedPreference(value = "samplingBucketValue", storeAsString = true, defaultValue = "0.0")
    private final double samplingBucketValue;

    @SharedPreference(
        value = "tagAutomation",
        defaultValue = "{}",
        mapKeyType = String.class,
        mapValueType = Boolean.class
    )
    private final Map<String, Boolean> tagAutomation;

    @SharedPreference(value = UserSettingsProperty.FAVORITE_SPORTS, defaultValue = "[]")
    private final int[] favoriteSports;

    @SharedPreference(value = UserSettingsProperty.MOTIVATIONS, defaultValue = "[]")
    private final String[] motivations;

    @SharedPreference(value = UserSettingsProperty.DISABLED_APP_RATING_SUGGESTIONS, defaultValue
        = "[]")
    private final String[] disabledAppRatingSuggestions;

    @SharedPreference(value = UserSettingsProperty.AUTOMATIC_UPDATE_DISABLED_WATCHES,
        defaultValue = "[]")
    private final String[] automaticUpdateDisabledWatches;

    @SharedPreference(value = "menstrualCycleRegularity", defaultValue = "NOT_SURE")
    private final MenstrualCycleRegularity menstrualCycleRegularity;

    @SharedPreference(value = "menstrualCycleLength", defaultValue = DEFAULT_MENSTRUAL_CYCLE_LENGTH)
    private final Integer menstrualCycleLength;

    @SharedPreference(value = "menstrualPeriodDuration", defaultValue =
        DEFAULT_MENSTRUAL_PERIOD_DURATION)
    private final Integer menstrualPeriodDuration;

    @SharedPreference(value = HR_REST_KEY, storeAsString = true, defaultValue = DEFAULT_REST_HR
        + "")
    private final int hrRest;

    @Nullable
    @SharedPreference(value = HR_ZONES_TYPE, jsonObjectType = HrZoneType.class, defaultValue = "{}")
    private final HrZoneType hrZoneType;
    @Nullable
    @SharedPreference(value = COMBINED_ZONES, jsonObjectType = IntensityZones.class,
        defaultValue = "{}")
    private final IntensityZones intensityZones;


    /**
     * A helper builder class for editing user settings values. Keep the fields in sync with
     * UserSettings.
     */
    private static class Builder {
        private String country;
        private String countrySubdivision;
        private String language;
        private MeasurementUnit measurementUnit;
        private int hrMaximum;
        private int wheelCircumference;
        private CadenceDataSource cadenceDataSource;
        private Sex gender;
        private Integer height;
        private Integer weight;
        private Long birthDate;
        private String analyticsUUID;
        private String email;
        private String phoneNumber;
        private ScreenBacklightSetting screenBacklight;
        private boolean keepAboveLock;
        private boolean gpsFiltering;
        private float altitudeOffset;
        private String selectedMapType;
        private boolean locallyChanged;
        private AltitudeSource altitudeSource;
        private boolean altitudeSourceSetByUser;
        private boolean notificationSoundEnabled;
        private boolean notificationWorkoutCommentedPushEnabled;
        private boolean notificationWorkoutSharedPushEnabled;
        private boolean notificationWorkoutReactedPushEnabled;
        private boolean notificationFacebookFriendJoinedPushEnabled;
        private boolean notificationNewFollowerPushEnabled;
        private boolean notificationNewActivitySyncedLocalEnabled;
        private boolean notificationWorkoutCommentedEmailEnabled;
        private boolean notificationNewFollowerEmailEnabled;
        private boolean notificationWorkoutSharedEmailEnabled;
        private boolean notificationDigestEmailEnabled;
        private boolean autoApproveFollowersEnabled;
        private long optinAccepted;
        private long optinRejected;
        private long optinLastShown;
        private long optinShowCount;
        private String realName;
        private String description;
        private int sharingFlagPreference;
        private boolean hasOutboundPartnerConnections;
        private String[] predefinedReplies;
        private Map<Integer, String> preferredTssCalculationMethods;
        private DayOfWeek firstDayOfTheWeek;
        private Map<String, Boolean> tagAutomation;
        private int[] favoriteSports;
        private String[] motivations;
        private String[] disabledAppRatingSuggestions;
        private String[] automaticUpdateDisabledWatches;
        private boolean privateAccount;
        private boolean showLocale;
        private double samplingBucketValue;

        private MenstrualCycleRegularity menstrualCycleRegularity;

        private Integer menstrualCycleLength;

        private Integer menstrualPeriodDuration;
        private Integer hrRest;
        private HrZoneType hrZoneType;
        private IntensityZones userIntensityZones;

        private Long lastAgeSaveTimestamp;
        private Long lastGenderSaveTimestamp;
        private Long lastWeightSaveTimestamp;
        private Long lastHeightSaveTimestamp;
        private Long lastMaxHRSaveTimestamp;
        private Long lastRestHRSaveTimestamp;

        Builder(UserSettings settings) {
            country = settings.country;
            countrySubdivision = settings.countrySubdivision;
            language = settings.language;
            measurementUnit = settings.measurementUnit;
            hrMaximum = settings.hrMaximum;
            wheelCircumference = settings.wheelCircumference;
            cadenceDataSource = settings.cadenceDataSource;
            gender = settings.gender;
            height = settings.height;
            weight = settings.weight;
            birthDate = settings.birthDate;
            analyticsUUID = settings.analyticsUUID;
            email = settings.email;
            screenBacklight = settings.screenBacklight;
            keepAboveLock = settings.keepAboveLock;
            gpsFiltering = settings.gpsFiltering;
            altitudeOffset = settings.altitudeOffset;
            selectedMapType = settings.selectedMapType;
            locallyChanged = settings.locallyChanged;
            altitudeSource = settings.altitudeSource;
            altitudeSourceSetByUser = settings.altitudeSourceSetByUser;
            notificationSoundEnabled = settings.notificationSoundEnabled;
            notificationWorkoutCommentedPushEnabled =
                settings.notificationWorkoutCommentedPushEnabled;
            notificationWorkoutSharedPushEnabled = settings.notificationWorkoutSharedPushEnabled;
            notificationWorkoutReactedPushEnabled = settings.notificationWorkoutReactedPushEnabled;
            notificationFacebookFriendJoinedPushEnabled =
                settings.notificationFacebookFriendJoinedPushEnabled;
            notificationNewFollowerPushEnabled = settings.notificationNewFollowerPushEnabled;
            notificationNewActivitySyncedLocalEnabled = settings.notificationNewActivitySyncedLocalEnabled;
            notificationWorkoutCommentedEmailEnabled =
                settings.notificationWorkoutCommentedEmailEnabled;
            notificationNewFollowerEmailEnabled = settings.notificationNewFollowerEmailEnabled;
            notificationWorkoutSharedEmailEnabled = settings.notificationWorkoutSharedEmailEnabled;
            notificationDigestEmailEnabled = settings.notificationDigestEmailEnabled;
            autoApproveFollowersEnabled = settings.autoApproveFollowersEnabled;
            optinAccepted = settings.optinAccepted;
            optinRejected = settings.optinRejected;
            optinLastShown = settings.optinLastShown;
            optinShowCount = settings.optinShowCount;
            realName = settings.realName;
            description = settings.description;
            sharingFlagPreference = settings.sharingFlagPreference;
            hasOutboundPartnerConnections = settings.hasOutboundPartnerConnections;
            phoneNumber = settings.phoneNumber;
            predefinedReplies = settings.predefinedReplies;
            preferredTssCalculationMethods = settings.preferredTssCalculationMethods;
            firstDayOfTheWeek = settings.firstDayOfTheWeek;
            tagAutomation = settings.tagAutomation;
            favoriteSports = settings.favoriteSports;
            motivations = settings.motivations;
            disabledAppRatingSuggestions = settings.disabledAppRatingSuggestions;
            automaticUpdateDisabledWatches = settings.automaticUpdateDisabledWatches;
            samplingBucketValue = settings.samplingBucketValue;
            privateAccount = settings.privateAccount;
            showLocale = settings.showLocale;
            menstrualCycleRegularity = settings.menstrualCycleRegularity;
            menstrualCycleLength = settings.menstrualCycleLength;
            menstrualPeriodDuration = settings.menstrualPeriodDuration;
            hrRest = settings.hrRest;
            hrZoneType = settings.hrZoneType;
            userIntensityZones = settings.intensityZones;
            lastAgeSaveTimestamp = settings.lastAgeSaveTimestamp;
            lastGenderSaveTimestamp = settings.lastGenderSaveTimestamp;
            lastWeightSaveTimestamp = settings.lastWeightSaveTimestamp;
            lastHeightSaveTimestamp = settings.lastHeightSaveTimestamp;
            lastMaxHRSaveTimestamp = settings.lastMaxHRSaveTimestamp;
            lastRestHRSaveTimestamp = settings.lastRestHRSaveTimestamp;
        }

        UserSettings build() {
            return new UserSettings(country, countrySubdivision, language,
                measurementUnit, hrMaximum, wheelCircumference, cadenceDataSource,
                gender, height, weight, birthDate, analyticsUUID, email, phoneNumber, screenBacklight,
                keepAboveLock, gpsFiltering,
                altitudeOffset, selectedMapType, locallyChanged, altitudeSource,
                altitudeSourceSetByUser,
                notificationSoundEnabled, notificationWorkoutCommentedPushEnabled,
                notificationWorkoutSharedPushEnabled, notificationWorkoutReactedPushEnabled,
                notificationFacebookFriendJoinedPushEnabled, notificationNewFollowerPushEnabled,
                notificationNewActivitySyncedLocalEnabled,
                notificationWorkoutCommentedEmailEnabled, notificationNewFollowerEmailEnabled,
                notificationWorkoutSharedEmailEnabled, notificationDigestEmailEnabled,
                autoApproveFollowersEnabled, optinAccepted, optinRejected, optinLastShown,
                optinShowCount, realName, description, sharingFlagPreference,
                hasOutboundPartnerConnections,
                predefinedReplies, preferredTssCalculationMethods, firstDayOfTheWeek,
                tagAutomation, favoriteSports, motivations, disabledAppRatingSuggestions,
                automaticUpdateDisabledWatches, samplingBucketValue, privateAccount,
                menstrualCycleRegularity, menstrualCycleLength, menstrualPeriodDuration,
                hrRest, hrZoneType, userIntensityZones, lastAgeSaveTimestamp,
                lastGenderSaveTimestamp, lastWeightSaveTimestamp, lastHeightSaveTimestamp,
                lastMaxHRSaveTimestamp, lastRestHRSaveTimestamp, showLocale);
        }
    }

    private Builder toBuilder() {
        return new Builder(this);
    }

    /**
     * Creates default settings.
     */
    public UserSettings() {
        this("",
            "",
            "",
            DEFAULT_MEASUREMENT_UNIT,
            DEFAULT_MAX_HR,
            DEFAULT_WHEEL_CIRCUMFERENCE,
            CadenceDataSource.DEFAULT,
            Sex.MALE,
            DEFAULT_HEIGHT_CENTIMETERS,
            DEFAULT_WEIGHT_GRAMS,
            DEFAULT_BIRTH_DATE,
            DEFAULT_USER_ANALYTICS_UUID,
            "",
            "",
            ScreenBacklightSetting.DEFAULT,
            true,
            true,
            0,
            MAP_TYPE_DEFAULT,
            false,
            AltitudeSource.DEFAULT,
            false,
            NotificationSettings.builder().build(),
            -1,
            -1,
            -1,
            -1,
            "",
            "",
            Integer.parseInt(DEFAULT_USER_SETTINGS_SHARE_PRIVACY),
            false,
            new String[] {},
            new HashMap<>(),
            DayOfWeek.MONDAY,
            new HashMap<>(),
            new int[] {},
            new String[] {},
            new String[] {},
            new String[] {},
            0.0,
            MenstrualCycleRegularity.NOT_SURE,
            Integer.parseInt(DEFAULT_MENSTRUAL_CYCLE_LENGTH),
            Integer.parseInt(DEFAULT_MENSTRUAL_PERIOD_DURATION),
            DEFAULT_REST_HR,
            new HrZoneType(HrType.MAX, 0L),
            null,
            0L,
            0L,
            0L,
            0L,
            0L,
            0L,
            false
        );
    }

    private UserSettings(Context context) {
        this(getDefaultCountry(context),
            getDefaultCountrySubdivision(context),
            getDefaultLanguage(),
            getDefaultMeasurementUnit(context),
            DEFAULT_MAX_HR,
            DEFAULT_WHEEL_CIRCUMFERENCE,
            CadenceDataSource.DEFAULT,
            Sex.MALE,
            DEFAULT_HEIGHT_CENTIMETERS,
            DEFAULT_WEIGHT_GRAMS,
            DEFAULT_BIRTH_DATE,
            DEFAULT_USER_ANALYTICS_UUID,
            "",
            "",
            ScreenBacklightSetting.DEFAULT,
            true,
            true,
            0,
            MAP_TYPE_DEFAULT,
            false,
            AltitudeSource.DEFAULT,
            false,
            NotificationSettings.builder().build(),
            -1,
            -1,
            -1,
            -1,
            "",
            "",
            Integer.parseInt(DEFAULT_USER_SETTINGS_SHARE_PRIVACY),
            false,
            getDefaultPredefinedAnswers(context),
            new HashMap<>(),
            getDefaultFirstDayOfTheWeek(context),
            new HashMap<>(),
            new int[] {},
            new String[] {},
            new String[] {},
            new String[] {},
            0.0,
            MenstrualCycleRegularity.NOT_SURE,
            Integer.parseInt(DEFAULT_MENSTRUAL_CYCLE_LENGTH),
            Integer.parseInt(DEFAULT_MENSTRUAL_PERIOD_DURATION),
            DEFAULT_REST_HR,
            new HrZoneType(HrType.MAX, 0L),
            null,
            0L,
            0L,
            0L,
            0L,
            0L,
            0L, false
        );
    }

    public static String[] getDefaultPredefinedAnswers(Context context) {
        return context.getResources().getStringArray(R.array.default_predefined_replies);
    }

    public UserSettings(String country, String countrySubdivision, String language,
        MeasurementUnit measurementUnit, int hrMaximum, int wheelCircumference,
        CadenceDataSource cadenceDataSource, Sex gender, Integer height, Integer weight, Long birthDate,
        String analyticsUUID, String email, String phoneNumber,
        ScreenBacklightSetting screenBacklight, boolean keepAboveLock,
        boolean gpsFiltering, float altitudeOffset, String selectedMapType, boolean locallyChanged,
        AltitudeSource altitudeSource, boolean altitudeSourceSetByUser,
        NotificationSettings notificationSettings, long optinAccepted, long optinRejected,
        long optinLastShown, long optinShowCount, String realName, String description,
        int sharingFlagPreference, boolean hasOutboundPartnerConnections,
        String[] predefinedReplies, Map<Integer, String> preferredTssCalculationMethods,
        DayOfWeek firstDayOfTheWeek, Map<String, Boolean> tagAutomation,
        int[] favoriteSports, String[] motivations, String[] disabledAppRatingSuggestions,
        String[] automaticUpdateDisabledWatches, double samplingBucketValue,
        MenstrualCycleRegularity menstrualCycleRegularity, Integer menstrualCycleLength,
        Integer menstrualPeriodDuration, @NonNull Integer hrRest, @Nullable HrZoneType hrZoneType,
        @Nullable IntensityZones intensityZones,
        Long lastAgeSaveTimestamp, Long lastGenderSaveTimestamp,
        Long lastWeightSaveTimestamp, Long lastHeightSaveTimestamp,
        Long lastMaxHRSaveTimestamp, Long lastRestHRSaveTimestamp, boolean showLocale) {
        this.country = country;
        this.countrySubdivision = countrySubdivision;
        this.language = language;
        this.measurementUnit = measurementUnit;
        this.hrMaximum = hrMaximum;
        this.wheelCircumference = wheelCircumference;
        this.cadenceDataSource = cadenceDataSource;
        this.gender = gender;
        this.height = height;
        this.weight = weight;
        this.birthDate = birthDate;
        this.analyticsUUID = analyticsUUID;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.screenBacklight = screenBacklight;
        this.keepAboveLock = keepAboveLock;
        this.gpsFiltering = gpsFiltering;
        this.altitudeOffset = altitudeOffset;
        this.selectedMapType = selectedMapType;
        this.locallyChanged = locallyChanged;
        this.altitudeSource = altitudeSource;
        this.hasOutboundPartnerConnections = hasOutboundPartnerConnections;
        this.altitudeSourceSetByUser = altitudeSourceSetByUser;
        notificationSoundEnabled = notificationSettings.notificationSoundEnabled();
        notificationWorkoutCommentedPushEnabled = notificationSettings.workoutCommentPushEnabled();
        notificationWorkoutSharedPushEnabled = notificationSettings.workoutSharePushEnabled();
        notificationWorkoutReactedPushEnabled = notificationSettings.workoutReactionPushEnabled();
        notificationFacebookFriendJoinedPushEnabled =
            notificationSettings.facebookFriendJoinPushEnabled();
        notificationNewFollowerPushEnabled = notificationSettings.newFollowerPushEnabled();
        notificationNewActivitySyncedLocalEnabled = notificationSettings.newActivitySyncedLocalEnabled();
        notificationWorkoutCommentedEmailEnabled =
            notificationSettings.workoutCommentEmailEnabled();
        notificationNewFollowerEmailEnabled = notificationSettings.newFollowerEmailEnabled();
        notificationWorkoutSharedEmailEnabled = notificationSettings.workoutShareEmailEnabled();
        notificationDigestEmailEnabled = notificationSettings.digestEmailEnabled();
        autoApproveFollowersEnabled = notificationSettings.autoApproveFollowersEnabled();
        this.optinAccepted = optinAccepted;
        this.optinRejected = optinRejected;
        this.optinLastShown = optinLastShown;
        this.optinShowCount = optinShowCount;
        this.realName = realName;
        this.description = description;
        this.sharingFlagPreference = sharingFlagPreference;
        this.predefinedReplies = predefinedReplies;
        this.preferredTssCalculationMethods = preferredTssCalculationMethods;
        this.firstDayOfTheWeek = firstDayOfTheWeek;
        this.tagAutomation = tagAutomation;
        this.favoriteSports = favoriteSports;
        this.motivations = motivations;
        this.disabledAppRatingSuggestions = disabledAppRatingSuggestions;
        this.automaticUpdateDisabledWatches = automaticUpdateDisabledWatches;
        this.samplingBucketValue = samplingBucketValue;
        this.privateAccount = notificationSettings.privateAccount();
        this.showLocale = showLocale;
        this.menstrualCycleRegularity = menstrualCycleRegularity;
        this.menstrualCycleLength = menstrualCycleLength;
        this.menstrualPeriodDuration = menstrualPeriodDuration;
        this.hrRest = hrRest;
        this.hrZoneType = hrZoneType;
        this.intensityZones = intensityZones;
        this.lastAgeSaveTimestamp = lastAgeSaveTimestamp;
        this.lastGenderSaveTimestamp = lastGenderSaveTimestamp;
        this.lastWeightSaveTimestamp = lastWeightSaveTimestamp;
        this.lastHeightSaveTimestamp = lastHeightSaveTimestamp;
        this.lastMaxHRSaveTimestamp = lastMaxHRSaveTimestamp;
        this.lastRestHRSaveTimestamp = lastRestHRSaveTimestamp;
    }

    private UserSettings(String country, String countrySubdivision, String language,
        MeasurementUnit measurementUnit, int hrMaximum, int wheelCircumference,
        CadenceDataSource cadenceDataSource, Sex gender, Integer height, Integer weight, Long birthDate,
        String analyticsUUID, String email, String phoneNumber,
        ScreenBacklightSetting screenBacklight, boolean keepAboveLock,
        boolean gpsFiltering, float altitudeOffset, String selectedMapType, boolean locallyChanged,
        AltitudeSource altitudeSource, boolean altitudeSourceSetByUser,
        boolean notificationSoundEnabled, boolean notificationWorkoutCommentedPushEnabled,
        boolean notificationWorkoutSharedPushEnabled, boolean notificationWorkoutReactedPushEnabled,
        boolean notificationFacebookFriendJoinedPushEnabled,
        boolean notificationNewFollowerPushEnabled,
        boolean notificationNewActivitySyncedLocalEnabled,
        boolean notificationWorkoutCommentedEmailEnabled,
        boolean notificationNewFollowerEmailEnabled, boolean notificationWorkoutSharedEmailEnabled,
        boolean notificationDigestEmailEnabled, boolean autoApproveFollowersEnabled,
        long optinAccepted, long optinRejected, long optinLastShown, long optinShowCount,
        String realName, String description, int sharingFlagPreference,
        boolean hasOutboundPartnerConnections,
        String[] predefinedReplies,
        Map<Integer, String> preferredTssCalculationMethods,
        DayOfWeek firstDayOfTheWeek, Map<String, Boolean> tagAutomation,
        int[] favoriteSports, String[] motivations, String[] disabledAppRatingSuggestions,
        String[] automaticUpdateDisabledWatches, double samplingBucketValue, boolean privateAccount,
        MenstrualCycleRegularity menstrualCycleRegularity, Integer menstrualCycleLength,
        Integer menstrualPeriodDuration,Integer hrRest, @Nullable HrZoneType hrZoneType,
        @Nullable IntensityZones intensityZones,
        Long lastAgeSaveTimestamp, Long lastGenderSaveTimestamp,
        Long lastWeightSaveTimestamp, Long lastHeightSaveTimestamp,
        Long lastMaxHRSaveTimestamp, Long lastRestHRSaveTimestamp, boolean showLocale) {
        this.measurementUnit = measurementUnit;
        this.country = country;
        this.countrySubdivision = countrySubdivision;
        this.language = language;
        this.hrMaximum = hrMaximum;
        this.wheelCircumference = wheelCircumference;
        this.cadenceDataSource = cadenceDataSource;
        this.gender = gender;
        this.height = height;
        this.weight = weight;
        this.birthDate = birthDate;
        this.analyticsUUID = analyticsUUID;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.screenBacklight = screenBacklight;
        this.keepAboveLock = keepAboveLock;
        this.gpsFiltering = gpsFiltering;
        this.altitudeOffset = altitudeOffset;
        this.selectedMapType = selectedMapType;
        this.locallyChanged = locallyChanged;
        this.altitudeSource = altitudeSource;
        this.altitudeSourceSetByUser = altitudeSourceSetByUser;
        this.notificationSoundEnabled = notificationSoundEnabled;
        this.notificationWorkoutCommentedPushEnabled = notificationWorkoutCommentedPushEnabled;
        this.notificationWorkoutSharedPushEnabled = notificationWorkoutSharedPushEnabled;
        this.notificationWorkoutReactedPushEnabled = notificationWorkoutReactedPushEnabled;
        this.notificationFacebookFriendJoinedPushEnabled =
            notificationFacebookFriendJoinedPushEnabled;
        this.notificationNewFollowerPushEnabled = notificationNewFollowerPushEnabled;
        this.notificationNewActivitySyncedLocalEnabled = notificationNewActivitySyncedLocalEnabled;
        this.notificationWorkoutCommentedEmailEnabled = notificationWorkoutCommentedEmailEnabled;
        this.notificationNewFollowerEmailEnabled = notificationNewFollowerEmailEnabled;
        this.notificationWorkoutSharedEmailEnabled = notificationWorkoutSharedEmailEnabled;
        this.notificationDigestEmailEnabled = notificationDigestEmailEnabled;
        this.autoApproveFollowersEnabled = autoApproveFollowersEnabled;
        this.optinAccepted = optinAccepted;
        this.optinRejected = optinRejected;
        this.optinLastShown = optinLastShown;
        this.optinShowCount = optinShowCount;
        this.realName = realName;
        this.description = description;
        this.sharingFlagPreference = sharingFlagPreference;
        this.hasOutboundPartnerConnections = hasOutboundPartnerConnections;
        this.predefinedReplies = predefinedReplies;
        this.preferredTssCalculationMethods = preferredTssCalculationMethods;
        this.firstDayOfTheWeek = firstDayOfTheWeek;
        this.tagAutomation = tagAutomation;
        this.favoriteSports = favoriteSports;
        this.motivations = motivations;
        this.disabledAppRatingSuggestions = disabledAppRatingSuggestions;
        this.automaticUpdateDisabledWatches = automaticUpdateDisabledWatches;
        this.samplingBucketValue = samplingBucketValue;
        this.privateAccount = privateAccount;
        this.showLocale = showLocale;
        this.menstrualCycleRegularity = menstrualCycleRegularity;
        this.menstrualCycleLength = menstrualCycleLength;
        this.menstrualPeriodDuration = menstrualPeriodDuration;
        this.hrRest = hrRest;
        this.hrZoneType = hrZoneType;
        this.intensityZones = intensityZones;
        this.lastAgeSaveTimestamp = lastAgeSaveTimestamp;
        this.lastGenderSaveTimestamp = lastGenderSaveTimestamp;
        this.lastWeightSaveTimestamp = lastWeightSaveTimestamp;
        this.lastHeightSaveTimestamp = lastHeightSaveTimestamp;
        this.lastMaxHRSaveTimestamp = lastMaxHRSaveTimestamp;
        this.lastRestHRSaveTimestamp = lastRestHRSaveTimestamp;
    }

    @Override
    public boolean equals(Object other) {
        if (!(other instanceof UserSettings)) {
            return false;
        }
        UserSettings settings = (UserSettings) other;
        if (!Objects.equals(this.country, settings.country)) {
            return false;
        }
        if (!Objects.equals(this.countrySubdivision, settings.countrySubdivision)) {
            return false;
        }
        if (!Objects.equals(this.language, settings.language)) {
            return false;
        }
        if (this.measurementUnit != settings.measurementUnit) {
            return false;
        }
        if (this.hrMaximum != settings.hrMaximum) {
            return false;
        }
        if (this.wheelCircumference != settings.wheelCircumference) {
            return false;
        }
        if (this.cadenceDataSource != settings.cadenceDataSource) {
            return false;
        }
        if (this.gender != settings.gender) {
            return false;
        }
        if (!Objects.equals(this.height, settings.height)) {
            return false;
        }
        if (!Objects.equals(this.weight, settings.weight)) {
            return false;
        }
        if (!Objects.equals(this.birthDate, settings.birthDate)) {
            return false;
        }
        if (!Objects.equals(this.analyticsUUID, settings.analyticsUUID)) {
            return false;
        }
        if (!Objects.equals(this.email, settings.email)) {
            return false;
        }
        if (this.screenBacklight != settings.screenBacklight) {
            return false;
        }
        if (this.keepAboveLock != settings.keepAboveLock) {
            return false;
        }
        if (this.gpsFiltering != settings.gpsFiltering) {
            return false;
        }
        if ((int) this.altitudeOffset != (int) settings.altitudeOffset) {
            return false;
        }
        if (!Objects.equals(this.selectedMapType, settings.selectedMapType)) {
            return false;
        }
        if (this.locallyChanged != settings.locallyChanged) {
            return false;
        }
        if (this.altitudeSource != settings.altitudeSource) {
            return false;
        }
        if (this.altitudeSourceSetByUser != settings.altitudeSourceSetByUser) {
            return false;
        }
        if (this.notificationSoundEnabled != settings.notificationSoundEnabled) {
            return false;
        }
        if (this.notificationWorkoutCommentedPushEnabled
            != settings.notificationWorkoutCommentedPushEnabled) {
            return false;
        }
        if (this.notificationWorkoutSharedPushEnabled
            != settings.notificationWorkoutSharedPushEnabled) {
            return false;
        }
        if (this.notificationWorkoutReactedPushEnabled
            != settings.notificationWorkoutReactedPushEnabled) {
            return false;
        }
        if (this.notificationFacebookFriendJoinedPushEnabled
            != settings.notificationFacebookFriendJoinedPushEnabled) {
            return false;
        }
        if (this.notificationNewFollowerPushEnabled
            != settings.notificationNewFollowerPushEnabled) {
            return false;
        }
        if (this.notificationNewActivitySyncedLocalEnabled
            != settings.notificationNewActivitySyncedLocalEnabled) {
            return false;
        }
        if (this.notificationWorkoutCommentedEmailEnabled
            != settings.notificationWorkoutCommentedEmailEnabled) {
            return false;
        }
        if (this.notificationNewFollowerEmailEnabled
            != settings.notificationNewFollowerEmailEnabled) {
            return false;
        }
        if (this.notificationWorkoutSharedEmailEnabled
            != settings.notificationWorkoutSharedEmailEnabled) {
            return false;
        }
        if (this.notificationDigestEmailEnabled != settings.notificationDigestEmailEnabled) {
            return false;
        }
        if (this.autoApproveFollowersEnabled != settings.autoApproveFollowersEnabled) {
            return false;
        }
        if (this.optinAccepted != settings.optinAccepted) {
            return false;
        }
        if (this.optinRejected != settings.optinRejected) {
            return false;
        }
        if (this.optinLastShown != settings.optinLastShown) {
            return false;
        }
        if (this.optinShowCount != settings.optinShowCount) {
            return false;
        }
        if (!Objects.equals(this.realName, settings.realName)) {
            return false;
        }
        if (!Objects.equals(this.description, settings.description)) {
            return false;
        }
        if (this.sharingFlagPreference != settings.sharingFlagPreference) {
            return false;
        }
        if (this.hasOutboundPartnerConnections != settings.hasOutboundPartnerConnections) {
            return false;
        }
        if (!Objects.equals(this.phoneNumber, settings.phoneNumber)) {
            return false;
        }
        if (!Arrays.equals(this.predefinedReplies, settings.predefinedReplies)) {
            return false;
        }
        if (!Objects.equals(this.preferredTssCalculationMethods,
            settings.preferredTssCalculationMethods)) {
            return false;
        }
        if (firstDayOfTheWeek != settings.firstDayOfTheWeek) {
            return false;
        }
        if (!Arrays.equals(this.favoriteSports, settings.favoriteSports)) {
            return false;
        }
        if (!Arrays.equals(this.motivations, settings.motivations)) {
            return false;
        }
        if (!Objects.equals(this.tagAutomation, settings.tagAutomation)) {
            return false;
        }
        if (!Arrays.equals(this.disabledAppRatingSuggestions,
            settings.disabledAppRatingSuggestions)) {
            return false;
        }

        if (!Objects.equals(this.automaticUpdateDisabledWatches,
            settings.automaticUpdateDisabledWatches)) {
            return false;
        }

        if (!Objects.equals(this.samplingBucketValue, settings.samplingBucketValue)) {
            return false;
        }

        if (this.privateAccount != settings.privateAccount) {
            return false;
        }
        if (this.showLocale != settings.showLocale) {
            return false;
        }


        if (this.menstrualCycleRegularity != settings.menstrualCycleRegularity) {
            return false;
        }

        if (!Objects.equals(this.menstrualCycleLength, settings.menstrualCycleLength)) {
            return false;
        }

        if (!Objects.equals(this.menstrualPeriodDuration, settings.menstrualPeriodDuration)) {
            return false;
        }
        if (!Objects.equals(this.hrRest, settings.hrRest)) {
            return false;
        }
        if (!Objects.equals(this.hrZoneType, settings.hrZoneType)) {
            return false;
        }
        if (!Objects.equals(this.intensityZones, settings.intensityZones)) {
            return false;
        }
        if (!Objects.equals(this.lastAgeSaveTimestamp, settings.lastAgeSaveTimestamp)) {
            return false;
        }
        if (!Objects.equals(this.lastGenderSaveTimestamp, settings.lastGenderSaveTimestamp)) {
            return false;
        }
        if (!Objects.equals(this.lastWeightSaveTimestamp, settings.lastWeightSaveTimestamp)) {
            return false;
        }
        if (!Objects.equals(this.lastHeightSaveTimestamp, settings.lastHeightSaveTimestamp)) {
            return false;
        }
        if (!Objects.equals(this.lastMaxHRSaveTimestamp, settings.lastMaxHRSaveTimestamp)) {
            return false;
        }
        if (!Objects.equals(this.lastRestHRSaveTimestamp, settings.lastRestHRSaveTimestamp)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = Objects.hash(country, countrySubdivision, language, measurementUnit, hrMaximum,
            wheelCircumference, cadenceDataSource, gender, height, weight, birthDate, analyticsUUID, email,
            screenBacklight, keepAboveLock, gpsFiltering, altitudeOffset, selectedMapType,
            locallyChanged, altitudeSource, altitudeSourceSetByUser, notificationSoundEnabled,
            notificationWorkoutCommentedPushEnabled, notificationWorkoutSharedPushEnabled,
            notificationWorkoutReactedPushEnabled, notificationFacebookFriendJoinedPushEnabled,
            notificationNewFollowerPushEnabled, notificationNewActivitySyncedLocalEnabled,
            notificationWorkoutCommentedEmailEnabled,
            notificationNewFollowerEmailEnabled, notificationWorkoutSharedEmailEnabled,
            notificationDigestEmailEnabled, autoApproveFollowersEnabled, optinAccepted,
            optinRejected, optinLastShown, optinShowCount, realName, description,
            sharingFlagPreference, hasOutboundPartnerConnections, phoneNumber,
            preferredTssCalculationMethods, firstDayOfTheWeek, tagAutomation,
            disabledAppRatingSuggestions, samplingBucketValue, privateAccount, showLocale,
            menstrualCycleRegularity, menstrualCycleLength, menstrualPeriodDuration,
            hrRest, hrZoneType, intensityZones, lastAgeSaveTimestamp,
            lastGenderSaveTimestamp, lastWeightSaveTimestamp, lastHeightSaveTimestamp,
            lastMaxHRSaveTimestamp, lastRestHRSaveTimestamp);
        result = 31 * result + Arrays.hashCode(predefinedReplies);
        result = 31 * result + Arrays.hashCode(automaticUpdateDisabledWatches);
        return result;
    }

    private static MeasurementUnit getDefaultMeasurementUnit(Context context) {
        String countryIso = DeviceUtils.getSimCountryCode(context);
        // Use the device locale in case there's no SIM, network or telephony manager at all
        if (TextUtils.isEmpty(countryIso)) {
            Locale currentLocale = DeviceUtils.getSystemLevelLocale(context);
            countryIso = currentLocale.getCountry();
        }
        return getMeasurementUnitByCountryIso(countryIso);
    }

    public static String getDefaultCountry(Context context) {
        return DeviceUtils.getSimCountryCode(context).toUpperCase(Locale.US);
    }

    public static String getDefaultCountrySubdivision(Context context) {
        return LocaleUtils.getDefaultCountrySubdivision(getDefaultCountry(context));
    }

    public static String getDefaultLanguage() {
        return Locale.getDefault().getLanguage().toUpperCase();
    }

    public static DayOfWeek getDefaultFirstDayOfTheWeek(Context context) {
        Locale locale = DeviceUtils.getSystemLevelLocale(context);
        return WeekFields.of(locale).getFirstDayOfWeek();
    }

    public static UserSettings defaults(Context context) {
        return new UserSettings(context);
    }

    public String getCountry() {
        return country;
    }

    public String getCountrySubdivision() {
        return countrySubdivision;
    }

    public String getLanguage() {
        return language;
    }

    public MeasurementUnit getMeasurementUnit() {
        return measurementUnit;
    }

    public String getRealName() {
        return realName;
    }

    @Override
    public boolean isLocallyChanged() {
        return locallyChanged;
    }

    public ScreenBacklightSetting getScreenBacklight() {
        return screenBacklight;
    }

    public boolean isGpsFiltering() {
        return gpsFiltering;
    }

    public float getAltitudeOffset() {
        return altitudeOffset;
    }

    public int getHrMaximum() {
        return hrMaximum;
    }

    public int getWheelCircumference() {
        return wheelCircumference;
    }

    public CadenceDataSource getCadenceDataSource() {
        return cadenceDataSource;
    }

    public Sex getGender() {
        return gender;
    }

    public Integer getHeight() {
        return height;
    }

    public Integer getWeight() {
        return weight;
    }

    public Integer getWeightInKg() {
        return weight != null ? weight / 1000 : null;
    }

    public Long getBirthDate() {
        return birthDate;
    }

    public Boolean isShowLocale() {
        return showLocale;
    }
    public String getAnalyticsUUID() {
        return analyticsUUID;
    }

    public String getEmail() {
        return email;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public boolean isAltitudeSourceSetByUser() {
        return altitudeSourceSetByUser;
    }

    public long getOptinAccepted() {
        return optinAccepted;
    }

    public long getOptinRejected() {
        return optinRejected;
    }

    public long getOptinLastShown() {
        return optinLastShown;
    }

    public long getOptinShowCount() {
        return optinShowCount;
    }

    @NonNull
    public MapType getSelectedMapType() {
        return MapTypeHelper.INSTANCE.getOrDefault(selectedMapType, MapTypeHelper.INSTANCE.getDEFAULT_MAP_TYPE());
    }

    public boolean isNotificationWorkoutReactedPushEnabled() {
        return notificationWorkoutReactedPushEnabled;
    }

    public boolean isNotificationWorkoutCommentedPushEnabled() {
        return notificationWorkoutCommentedPushEnabled;
    }

    public boolean isNotificationNewFollowerPushEnabled() {
        return notificationNewFollowerPushEnabled;
    }

    public boolean isNotificationNewActivitySyncedLocalEnabled() {
        return notificationNewActivitySyncedLocalEnabled;
    }

    public boolean isNotificationFacebookFriendJoinedPushEnabled() {
        return notificationFacebookFriendJoinedPushEnabled;
    }

    public boolean isNotificationWorkoutSharedPushEnabled() {
        return notificationWorkoutSharedPushEnabled;
    }

    public String getDescription() {
        return description;
    }

    public int getSharingFlagPreference() {
        return sharingFlagPreference;
    }

    public boolean isKeepAboveLock() {
        return keepAboveLock;
    }

    public AltitudeSource getAltitudeSource() {
        return altitudeSource;
    }

    public boolean hasOutboundPartnerConnections() {
        return hasOutboundPartnerConnections;
    }

    public NotificationSettings getNotificationSettings() {
        return NotificationSettings.builder()
            .notificationSoundEnabled(notificationSoundEnabled)
            .workoutCommentPushEnabled(notificationWorkoutCommentedPushEnabled)
            .workoutSharePushEnabled(notificationWorkoutSharedPushEnabled)
            .workoutReactionPushEnabled(notificationWorkoutReactedPushEnabled)
            .facebookFriendJoinPushEnabled(notificationFacebookFriendJoinedPushEnabled)
            .newFollowerPushEnabled(notificationNewFollowerPushEnabled)
            .newActivitySyncedLocalEnabled(notificationNewActivitySyncedLocalEnabled)
            .workoutCommentEmailEnabled(notificationWorkoutCommentedEmailEnabled)
            .workoutShareEmailEnabled(notificationWorkoutSharedEmailEnabled)
            .newFollowerEmailEnabled(notificationNewFollowerEmailEnabled)
            .digestEmailEnabled(notificationDigestEmailEnabled)
            .autoApproveFollowersEnabled(autoApproveFollowersEnabled)
            .setPrivateAccount(privateAccount)
            .build();
    }

    public String[] getPredefinedReplies() {
        return predefinedReplies;
    }

    public Map<Integer, String> getPreferredTssCalculationMethods() {
        return preferredTssCalculationMethods;
    }

    public DayOfWeek getFirstDayOfTheWeek() {
        return firstDayOfTheWeek;
    }

    public Map<String, Boolean> getTagAutomation() {
        return tagAutomation;
    }

    public int[] getFavoriteSports() {
        return favoriteSports;
    }

    public String[] getMotivations() {
        return motivations;
    }

    public String[] getDisabledAppRatingSuggestions() {
        return disabledAppRatingSuggestions;
    }

    public String[] getAutomaticUpdateDisabledWatches() {
        return automaticUpdateDisabledWatches;
    }

    public double getSamplingBucketValue() {
        return samplingBucketValue;
    }

    public int getHrRest() {
        return hrRest;
    }

    @Nullable
    public HrZoneType getHrZoneType() {
        return hrZoneType;
    }

    public CombinedIntensityZones getCombinedZones() {
        return new CombinedIntensityZones(getHrZoneType(), intensityZones);
    }

    public long getLastAgeSaveTimestamp() {
        return lastAgeSaveTimestamp;
    }

    public long getLastGenderSaveTimestamp() {
        return lastGenderSaveTimestamp;
    }

    public long getLastWeightSaveTimestamp() {
        return lastWeightSaveTimestamp;
    }

    public long getLastHeightSaveTimestamp() {
        return lastHeightSaveTimestamp;
    }

    public long getLastMaxHRSaveTimestamp() {
        return lastMaxHRSaveTimestamp;
    }

    public long getLastRestHRSaveTimestamp() {
        return lastRestHRSaveTimestamp;
    }

    @Override
    public UserSettings synced() {
        Builder builder = toBuilder();
        builder.locallyChanged = false;
        return builder.build();
    }

    public UserSettings setRealName(String realName) {
        if (Objects.equals(this.realName, realName)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.realName = realName;
        return builder.build();
    }

    @NonNull
    public UserSettings setSelectedMapType(@NonNull MapType mapType) {
        String selectedMapType = mapType.getName();
        if (Objects.equals(this.selectedMapType, selectedMapType)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.selectedMapType = selectedMapType;
        return builder.build();
    }

    public UserSettings setAltitudeSource(AltitudeSource altitudeSource) {
        if (Objects.equals(this.altitudeSource, altitudeSource)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.altitudeSource = altitudeSource;
        return builder.build();
    }

    public UserSettings setShowLocale(boolean showLocale) {
        if (Objects.equals(this.showLocale, showLocale)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.showLocale = showLocale;
        return builder.build();
    }

    public UserSettings setSex(Sex gender) {
        if (Objects.equals(this.gender, gender)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.gender = gender;
        return builder.build();
    }

    public UserSettings setProfileDescription(String profileDescription) {
        if (Objects.equals(this.description, profileDescription)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.description = profileDescription;
        return builder.build();
    }

    public UserSettings setSharingFlagPreference(int sharingFlagPreference) {
        if (this.sharingFlagPreference == sharingFlagPreference) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.sharingFlagPreference = sharingFlagPreference;
        return builder.build();
    }

    public UserSettings copyWithEmail(String email, boolean requestBackendSync) {
        if (Objects.equals(this.email, email)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = requestBackendSync;
        builder.email = email;
        return builder.build();
    }

    public UserSettings setPhoneNumber(String phoneNumber) {
        if (Objects.equals(this.phoneNumber, phoneNumber)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.phoneNumber = phoneNumber;
        return builder.build();
    }

    public UserSettings setUserProfile(UserProfile userProfile) {
        float heightInMeter = userProfile.getHeightInMeter();
        int weightInGrams = userProfile.getWeightInGrams();
        Sex gender = Sex.valueOf(userProfile.getSex());
        long birthDate = DateUtils.getDefaultBirthDateFromYear(userProfile.getBirthYear());
        int maxHR = userProfile.getMaxHR();
        int restHR = userProfile.getRestHR();
        return this.setUserProfile(heightInMeter, weightInGrams, gender, birthDate, maxHR, restHR);
    }

    public UserSettings setUserProfile(
        float heightInMeter,
        int weightInGrams,
        Sex gender,
        long birthDate,
        int maxHR,
        int restHR
    ) {
        int heightInCentimeter = (int) (heightInMeter * 100);
        if (this.weight == weightInGrams &&
            this.height == heightInCentimeter &&
            Objects.equals(this.gender, gender) &&
            this.birthDate == birthDate &&
            this.hrMaximum == maxHR &&
            this.hrRest == restHR) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.height = heightInCentimeter;
        builder.weight = weightInGrams;
        builder.gender = gender;
        builder.birthDate = birthDate;
        builder.hrMaximum = maxHR;
        builder.hrRest = restHR;
        return builder.build();
    }

    public UserSettings setBirthDate(long birthDate) {
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.birthDate = birthDate;
        return builder.build();
    }

    public UserSettings setHrMaximum(int hrMaximum) {
        if (Objects.equals(this.hrMaximum, hrMaximum)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.hrMaximum = hrMaximum;
        return builder.build();
    }

    public UserSettings setHrRest(int hrRest) {
        if (Objects.equals(this.hrRest, hrRest)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.hrRest = hrRest;
        return builder.build();
    }

    public UserSettings setHrZoneType(HrZoneType hrZoneType) {
        if (Objects.equals(this.hrZoneType, hrZoneType)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.hrZoneType = hrZoneType;
        return builder.build();
    }

    public UserSettings setIntensityZones(IntensityZones intensityZones) {
        if (Objects.equals(this.intensityZones, intensityZones)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.userIntensityZones =
            DomainUserSettingsKt.merge(intensityZones, this.intensityZones);
        return builder.build();
    }

    public UserSettings setCountry(String country) {
        if (Objects.equals(this.country, country)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.country = country;
        return builder.build();
    }

    public UserSettings setCountrySubdivision(String countrySubdivision) {
        if (Objects.equals(this.countrySubdivision, countrySubdivision)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.countrySubdivision = countrySubdivision;
        return builder.build();
    }

    public UserSettings setDefaultCountry(Context context) {
        String defaultCountry = getDefaultCountry(context);
        if (Objects.equals(this.country, defaultCountry)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.country = defaultCountry;
        return builder.build();
    }

    public UserSettings setDefaultCountrySubdivision(Context context) {
        String defaultCountrySubdivision = getDefaultCountrySubdivision(context);
        if (Objects.equals(this.countrySubdivision, defaultCountrySubdivision)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.countrySubdivision = defaultCountrySubdivision;
        return builder.build();
    }

    public UserSettings setDefaultLanguage() {
        String defaultLanguage = getDefaultLanguage();
        if (Objects.equals(this.language, defaultLanguage)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.language = defaultLanguage;
        return builder.build();
    }

    public UserSettings setDisabledAppRatingSuggestions(String[] disabledAppRatingSuggestions) {
        if (Arrays.equals(this.disabledAppRatingSuggestions, disabledAppRatingSuggestions)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.disabledAppRatingSuggestions = disabledAppRatingSuggestions;
        return builder.build();
    }

    public UserSettings updateNotificationSettings(NotificationSettings notificationSettings) {
        if (Objects.equals(getNotificationSettings(), notificationSettings)) {
            return this;
        }
        return new UserSettings(country, countrySubdivision, language,
            measurementUnit, hrMaximum, wheelCircumference, cadenceDataSource,
            gender, height, weight, birthDate, analyticsUUID, email, phoneNumber, screenBacklight,
            keepAboveLock, gpsFiltering,
            altitudeOffset, selectedMapType, true, altitudeSource, altitudeSourceSetByUser,
            notificationSettings, optinAccepted, optinRejected, optinLastShown, optinShowCount,
            realName, description, sharingFlagPreference, hasOutboundPartnerConnections,
            predefinedReplies, preferredTssCalculationMethods, firstDayOfTheWeek, tagAutomation,
            favoriteSports, motivations, disabledAppRatingSuggestions,
            automaticUpdateDisabledWatches, samplingBucketValue,
            menstrualCycleRegularity, menstrualCycleLength, menstrualPeriodDuration,
            hrRest, hrZoneType, intensityZones, lastAgeSaveTimestamp, lastGenderSaveTimestamp,
            lastWeightSaveTimestamp, lastHeightSaveTimestamp, lastMaxHRSaveTimestamp,
            lastRestHRSaveTimestamp, showLocale);
    }

    public UserSettings setPredefinedReplies(String[] predefinedReplies) {
        if (Arrays.equals(this.predefinedReplies, predefinedReplies)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.predefinedReplies = predefinedReplies;
        return builder.build();
    }

    public UserSettings setPreferredTssCalculationMethod(int workoutType, String method) {
        String current = preferredTssCalculationMethods.get(workoutType);
        if (current != null && current.equals(method)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        Map<Integer, String> newMethodsMap = new HashMap<>(preferredTssCalculationMethods);
        newMethodsMap.put(workoutType, method);
        builder.preferredTssCalculationMethods = newMethodsMap;
        return builder.build();
    }

    public UserSettings setTagAutomation(String tagKey, Boolean value) {
        Boolean current = tagAutomation.get(tagKey);
        if (current != null && current.equals(value)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        Map<String, Boolean> newMethodsMap = new HashMap<>(tagAutomation);
        newMethodsMap.put(tagKey, value);
        builder.tagAutomation = newMethodsMap;
        return builder.build();
    }

    public UserSettings setFavoriteSports(int[] favoriteSports) {
        if (Arrays.equals(this.favoriteSports, favoriteSports)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.favoriteSports = favoriteSports;
        return builder.build();
    }

    public UserSettings setMotivations(String[] motivations) {
        if (Arrays.equals(this.motivations, motivations)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.motivations = motivations;
        return builder.build();
    }

    public UserSettings setFirstDayOfTheWeek(DayOfWeek firstDayOfTheWeek) {
        if (this.firstDayOfTheWeek == firstDayOfTheWeek) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.firstDayOfTheWeek = firstDayOfTheWeek;

        return builder.build();
    }

    public UserSettings setAutomaticUpdatesDisabledWatches(
        String[] automaticUpdateDisabledWatches) {
        if (Arrays.equals(this.automaticUpdateDisabledWatches, automaticUpdateDisabledWatches)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.automaticUpdateDisabledWatches = automaticUpdateDisabledWatches;

        return builder.build();
    }

    public UserSettings setMenstrualCycleSettings(@Nullable MenstrualCycleSettings menstrualCycleSetting) {
        MenstrualCycleRegularity cycleRegularity;
        int cycleLength;
        int periodDuration;
        int defaultCycleLength = Integer.parseInt(DEFAULT_MENSTRUAL_CYCLE_LENGTH);
        if (menstrualCycleSetting == null) {
            cycleRegularity = MenstrualCycleRegularity.NOT_SURE;
            cycleLength = defaultCycleLength;
            periodDuration = Integer.parseInt(DEFAULT_MENSTRUAL_PERIOD_DURATION);
        } else  {
            cycleRegularity = menstrualCycleSetting.getCycleRegularity();
            cycleLength = menstrualCycleSetting.getCycleLength() == null ? defaultCycleLength : menstrualCycleSetting.getCycleLength();
            periodDuration = menstrualCycleSetting.getPeriodDuration();
        }
        if (this.menstrualCycleRegularity == cycleRegularity &&
            Objects.equals(this.menstrualCycleLength, cycleLength) &&
            Objects.equals(this.menstrualPeriodDuration, periodDuration)) {
            return this;
        }
        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.menstrualCycleRegularity = cycleRegularity;
        builder.menstrualCycleLength = cycleLength;
        builder.menstrualPeriodDuration = periodDuration;

        return builder.build();
    }

    public @Nullable MenstrualCycleSettings getMenstrualCycleSetting() {
        // Initially, they are the default values, which means that MC has not yet been used.
        if (menstrualCycleRegularity == MenstrualCycleRegularity.NOT_SURE &&
            String.valueOf(menstrualCycleLength).equals(DEFAULT_MENSTRUAL_CYCLE_LENGTH) &&
            String.valueOf(menstrualPeriodDuration).equals(DEFAULT_MENSTRUAL_PERIOD_DURATION)) {
            return null;
        }

        Integer cycleLength = String.valueOf(menstrualCycleLength).equals(DEFAULT_MENSTRUAL_CYCLE_LENGTH) ? null : menstrualCycleLength;
        return new MenstrualCycleSettings(menstrualCycleRegularity, cycleLength, menstrualPeriodDuration);
    }

    @NonNull
    public UserSettings setMeasurementUnit(@NonNull MeasurementUnit measurementUnit) {
        if (this.measurementUnit == measurementUnit) {
            return this;
        }

        Builder builder = toBuilder();
        builder.locallyChanged = true;
        builder.measurementUnit = measurementUnit;
        return builder.build();
    }
}
