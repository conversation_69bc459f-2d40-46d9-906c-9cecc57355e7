package com.stt.android.workoutdetail.workoutvalues.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workouts.details.values.WorkoutValue

@Composable
fun WorkoutValuesGridItem(
    workoutValueGridItem: WorkoutValuesGridItemData,
    onValueClicked: (WorkoutValue) -> Unit,
    modifier: Modifier = Modifier,
    valueTextStyle: TextStyle = MaterialTheme.typography.bodyLargeBold
) {
    val interactionSource = remember { MutableInteractionSource() }

    Column(
        modifier = modifier
            .background(MaterialTheme.colors.surface)
            .heightIn(min = 56.dp)
            .clickable(
                interactionSource = interactionSource,
                indication = ripple(),
                enabled = workoutValueGridItem.showMoreInfoIcon,
                onClick = { onValueClicked(workoutValueGridItem.workoutValue) }
            )
            .padding(
                vertical = MaterialTheme.spacing.smaller,
                horizontal = MaterialTheme.spacing.medium
            ),
        verticalArrangement = Arrangement.Top
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = workoutValueGridItem.name,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.weight(1f),
                color = MaterialTheme.colors.onSurface,
            )
            if (workoutValueGridItem.showMoreInfoIcon) {
                Icon(
                    painter = painterResource(id = R.drawable.workout_value_info),
                    tint = Color.Unspecified,
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.mini)
                )
            }
        }

        val drawableResId = workoutValueGridItem.workoutValue.drawableResId
        if (drawableResId != null) {
            Icon(
                painter = painterResource(id = drawableResId),
                tint = MaterialTheme.colors.nearBlack,
                contentDescription = null,
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.small)
                    .padding(top = MaterialTheme.spacing.xxsmall)
            )
        } else {
            Text(
                text = workoutValueGridItem.value,
                modifier = Modifier
                    .padding(top = MaterialTheme.spacing.xxsmall),
                color = MaterialTheme.colors.onSurface,
                style = valueTextStyle,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Preview(showBackground = true, widthDp = 180)
@Composable
private fun WorkoutValuesGridItemPreview() {
    AppTheme {
        WorkoutValuesGridItem(
            workoutValueGridItem = WorkoutValuesGridItemData(
                name = "Calories",
                value = "265 kcal",
                showMoreInfoIcon = true,
            ),
            onValueClicked = {}
        )
    }
}
