package com.stt.android.social.personalrecord

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.achievements.PersonalRecordType
import com.stt.android.domain.achievements.RecordItemDomain
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import javax.inject.Inject

@HiltViewModel
class PersonalRecordsViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val infoModelFormatter: InfoModelFormatter,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val personalRecordRepository: PersonalRecordRepository,
    private val workoutHeaderController: WorkoutHeaderController
) : ViewModel() {
    private val _uiState: MutableStateFlow<PersonalRecordsViewState> =
        MutableStateFlow(PersonalRecordsViewState.Loading)
    val uiState = _uiState.asStateFlow()

    var selectedActivityType: CoreActivityType = CoreActivityType.RUNNING

    init {
        trackEventPersonalRecordScreenEvent()
        observeReloadEvent()
        load()
    }

    private fun observeReloadEvent() {
        workoutHeaderController.currentUserWorkoutUpdated
            .onEach { load(forceFetchDataFromBackend = true) }
            .launchIn(viewModelScope)
    }

    private fun load(forceFetchDataFromBackend: Boolean = false) {
        Timber.d("PersonalRecordsViewModel: load")
        viewModelScope.launch {
            _uiState.value = PersonalRecordsViewState.Loading
            _uiState.value = runSuspendCatching {
                mapRemoteRecordToViewState(
                    if (forceFetchDataFromBackend) {
                        personalRecordRepository.fetchRemotePersonalRecordAndStoreLocal(true)
                    } else {
                        personalRecordRepository.fetchPersonalRecordFromDataSource()
                    }
                )
            }
                .getOrElse { e ->
                    Timber.w(e, "Failed to load personal records")
                    PersonalRecordsViewState.Error
                }
        }
    }

    private suspend fun mapRemoteRecordToViewState(recordResultItemList: List<PersonalRecordResultItem>): PersonalRecordsViewState.Loaded {
        val recordResultItem =
            recordResultItemList.find { it.activityId == selectedActivityType.id }

        val records = recordResultItem?.types?.mapNotNull { type ->
            type.mapRecordTypeToRecordItemDomain(recordResultItem.records)
        }

        return PersonalRecordsViewState.Loaded(records ?: emptyList())
    }

    private suspend fun String.mapRecordTypeToRecordItemDomain(records: Records) = when (this) {
        PersonalRecordType.FASTEST_PACE.recordRemoteType -> records.fastestPace.mapRecordType(
            PersonalRecordType.FASTEST_PACE,
            SummaryItem.AVGPACE
        )

        PersonalRecordType.HIGHEST_CLIMB.recordRemoteType -> records.highestClimb.mapRecordType(
            PersonalRecordType.HIGHEST_CLIMB,
            SummaryItem.ASCENTALTITUDE
        )

        PersonalRecordType.HIGHEST_ALTITUDE.recordRemoteType -> records.highestAltitude.mapRecordType(
            PersonalRecordType.HIGHEST_ALTITUDE,
            SummaryItem.HIGHALTITUDE
        )

        PersonalRecordType.LONGEST_DISTANCE.recordRemoteType -> records.longestDistance.mapRecordType(
            PersonalRecordType.LONGEST_DISTANCE,
            SummaryItem.DISTANCE
        )

        PersonalRecordType.HIGHEST_SPEED.recordRemoteType -> records.highestSpeed.mapRecordType(
            PersonalRecordType.HIGHEST_SPEED,
            SummaryItem.AVGSPEED
        )

        PersonalRecordType.RUNNING_5_KM.recordRemoteType -> records.fiveKilometers.mapRecordType(
            PersonalRecordType.RUNNING_5_KM,
            SummaryItem.DURATION
        )

        PersonalRecordType.RUNNING_10_KM.recordRemoteType -> records.tenKilometers.mapRecordType(
            PersonalRecordType.RUNNING_10_KM,
            SummaryItem.DURATION
        )

        PersonalRecordType.MARATHON_HALF_MARATHON_KM.recordRemoteType -> records.halfMarathon.mapRecordType(
            PersonalRecordType.MARATHON_HALF_MARATHON_KM,
            SummaryItem.DURATION
        )

        PersonalRecordType.MARATHON_FULL_MARATHON_KM.recordRemoteType -> records.fullMarathon.mapRecordType(
            PersonalRecordType.MARATHON_FULL_MARATHON_KM,
            SummaryItem.DURATION
        )

        else -> {
            Timber.d("Unknown remote record type: $this")
            null
        }
    }

    private suspend fun RemoteRecordItem?.mapRecordType(
        personalRecordType: PersonalRecordType,
        summaryItem: SummaryItem
    ) = RecordItemDomain(
        personalRecordType = personalRecordType,
        value = this?.let {
            infoModelFormatter.formatValueAsString(
                summaryItem,
                it.value
            )
        },
        timestamp = this?.date?.formatDate(),
        workoutHeader = this?.let { personalRecordRepository.loadWorkoutHeader(it.workoutKey) }
    )

    fun onFilterTagChanged(activityType: CoreActivityType) {
        if (selectedActivityType != activityType) {
            selectedActivityType = activityType
            _uiState.value = PersonalRecordsViewState.Loading
            load()
        }
    }

    private fun Long.formatDate(): String {
        val localDateTime =
            LocalDateTime.ofInstant(Instant.ofEpochMilli(this), ZoneId.systemDefault())
        return "${localDateTime.monthValue}.${localDateTime.dayOfMonth}.${localDateTime.year}"
    }

    private fun trackEventPersonalRecordScreenEvent() {
        val analyticsSource: String? = savedStateHandle[NAVIGATED_FROM_SOURCE]
        viewModelScope.launch {
            withContext(IO) {
                runSuspendCatching {
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.PERSONAL_RECORDS,
                        AnalyticsProperties().apply {
                            put(
                                AnalyticsEventProperty.SOURCE,
                                analyticsSource
                            )
                        }
                    )
                }
            }
        }
    }
}

sealed class PersonalRecordsViewState {

    data object Loading : PersonalRecordsViewState()

    data class Loaded(
        val recordList: List<RecordItemDomain>,
    ) : PersonalRecordsViewState()

    data object Error : PersonalRecordsViewState()
}
