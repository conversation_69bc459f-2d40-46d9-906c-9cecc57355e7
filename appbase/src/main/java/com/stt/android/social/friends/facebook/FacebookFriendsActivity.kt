package com.stt.android.social.friends.facebook

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.domain.STTErrorCodes
import com.stt.android.exceptions.BackendException
import com.stt.android.social.friends.Friend
import com.stt.android.social.userprofileV2.BaseUserProfileActivity
import com.stt.android.utils.getLocalizedErrorMessage
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

@AndroidEntryPoint
class FacebookFriendsActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            FacebookFriendsScreen()
        }
    }

    private fun onFriendClick(friend: Friend) {
        startActivity(
            BaseUserProfileActivity.newStartIntent(
                this,
                friend.username,
                false,
            )
        )
    }

    @Composable
    private fun FacebookFriendsScreen(
        modifier: Modifier = Modifier,
        viewModel: FacebookFriendsViewModel = hiltViewModel(),
    ) {
        val snackbarHostState = remember {
            SnackbarHostState()
        }
        val state by viewModel.friendsStateFlow.collectAsState()

        val retryText = stringResource(R.string.retry)
        val context = LocalContext.current
        LaunchedEffect(Unit) {
            viewModel.eventFlow.collect { event ->
                if (event !is FacebookFriendsFailedEvent) return@collect
                val isTooManyRequest =
                    event is FacebookFriendsFailedToFollow && event.throwable is BackendException && (event.throwable as BackendException).error == STTErrorCodes.TOO_MANY_FOLLOW_REQUESTS
                val message = event.throwable.getLocalizedErrorMessage(context)

                val result = snackbarHostState.showSnackbar(
                    message = message,
                    actionLabel = if (isTooManyRequest) null else retryText,
                    duration = SnackbarDuration.Short
                )

                if (result == SnackbarResult.ActionPerformed) {
                    when (event) {
                        is FacebookFriendsFailedToLoad -> viewModel.loadFacebookFriends()
                        is FacebookFriendsFailedToAddAll -> viewModel.onAddAllClick()
                        is FacebookFriendsFailedToFollow -> viewModel.onStatusClick(event.friend)
                    }
                }
            }
        }

        var addingAllSnackbarJob by remember { mutableStateOf<Job?>(null) }
        val addAllSnackbarMessage = pluralStringResource(
            R.plurals.fb_friends_added,
            state.unfollowingFriendCount,
            state.unfollowingFriendCount,
        )
        val addAllSnackbarAction = stringResource(R.string.undo)
        LaunchedEffect(state.addingAll) {
            if (state.addingAll) {
                addingAllSnackbarJob?.cancel()
                addingAllSnackbarJob = launch {
                    val result = snackbarHostState.showSnackbar(
                        message = addAllSnackbarMessage,
                        actionLabel = addAllSnackbarAction,
                        duration = SnackbarDuration.Indefinite,
                    )
                    if (result == SnackbarResult.ActionPerformed) {
                        viewModel.undoAddAll()
                    }
                }
            } else {
                addingAllSnackbarJob?.cancel()
                addingAllSnackbarJob = null
            }
        }

        Scaffold(
            snackbarHost = {
                SnackbarHost(hostState = snackbarHostState)
            },
            topBar = {
                TopBar(onBackClick = ::finish)
            },
            modifier = modifier,
        ) { paddingValues ->
            Surface(
                modifier = Modifier
                    .padding(paddingValues)
                    .narrowContentWithBgColors(
                        backgroundColor = MaterialTheme.colorScheme.surface,
                        outerBackgroundColor = MaterialTheme.colorScheme.background,
                    )
            ) {
                FacebookFriendsContent(
                    state = state,
                    onFriendClick = ::onFriendClick,
                    onStatusClick = viewModel::onStatusClick,
                    onAddAllClick = if (state.noFriend) ::finish else viewModel::onAddAllClick,
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun TopBar(
        onBackClick: () -> Unit,
        modifier: Modifier = Modifier,
    ) {
        TopAppBar(
            title = {
                Text(
                    text = stringResource(R.string.find_facebook_friends).uppercase(),
                )
            },
            modifier = modifier,
            navigationIcon = {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackClick,
                    contentDescription = stringResource(R.string.back),
                )
            },
        )
    }

    companion object {
        fun newStartIntent(context: Context): Intent {
            return Intent(context, FacebookFriendsActivity::class.java)
        }
    }
}
