package com.stt.android.social.workoutlistv2

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.flatMap
import androidx.paging.insertSeparators
import androidx.paging.map
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.user.User
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.remote.UserAgent
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER_NAME
import com.stt.android.social.workoutlistv2.data.WorkoutsPagingSource
import com.stt.android.social.workoutlistv2.usecase.LoadWorkoutPageUseCase
import com.stt.android.ui.components.workout.WorkoutCardViewData
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.map
import javax.inject.Inject

@HiltViewModel
class WorkoutPageViewModel @Inject constructor(
    @ApplicationContext private val appContext: Context,
    savedStateHandle: SavedStateHandle,
    currentUserController: CurrentUserController,
    @UserAgent private val userAgent: String,
    private val loadWorkoutPageUseCase: LoadWorkoutPageUseCase,
    val workoutCardActionsHandler: WorkoutCardActionsHandler,
) : ViewModel() {
    val username: String = savedStateHandle.get<String>(KEY_USER_NAME)
        ?: savedStateHandle.get<User>(KEY_USER)?.username
        ?: currentUserController.username

    val workoutPagingDataFlow = getWorkoutPager(currentUserController.username)
        .flow
        .map { page ->
            page.mapToListItem()
        }
        .cachedIn(viewModelScope)
    val mediaPagingDataFlow = getWorkoutPager(currentUserController.username, withMediaOnly = true)
        .flow
        .map { page ->
            page.mapToMediaItem()
        }
        .cachedIn(viewModelScope)

    private fun PagingData<WorkoutCardInfo>.mapToListItem(): PagingData<WorkoutCardItem> {
        return map { WorkoutCardItem.Workout(it) }
            .insertSeparators { before, after ->
                val beforeTime =
                    before?.data?.workoutHeader?.startTime
                val afterTime = after?.data?.workoutHeader?.startTime

                if (afterTime == null) return@insertSeparators null // end of list

                val afterYearMonth = TextFormatter.formatYearMonth(appContext, afterTime)
                val beforeYearMonth =
                    beforeTime?.let { TextFormatter.formatYearMonth(appContext, it) }

                if (afterYearMonth != beforeYearMonth) {
                    WorkoutCardItem.Header(afterYearMonth)
                } else null
            }
    }

    private fun PagingData<WorkoutCardInfo>.mapToMediaItem(): PagingData<WorkoutMediaItem> {
        return flatMap { workoutCardInfo ->
            val workoutHeader = workoutCardInfo.workoutHeader
            workoutCardInfo.workoutCardViewData.coverInfo.mapNotNull {
                when (it) {
                    is WorkoutCardViewData.CoverInfo.Map -> null
                    is WorkoutCardViewData.CoverInfo.Image -> WorkoutMediaItem.Photo(it.uri, workoutHeader)
                    is WorkoutCardViewData.CoverInfo.Video -> WorkoutMediaItem.Video(it.uri, workoutHeader, userAgent)
                }
            }
        }
    }

    private fun getWorkoutPager(username: String, withMediaOnly: Boolean = false): Pager<Int, WorkoutCardInfo> {
        return Pager(
            config = PagingConfig(
                pageSize = PAGE_SIZE,
                enablePlaceholders = false,
            ),
            pagingSourceFactory = {
                WorkoutsPagingSource(username, loadWorkoutPageUseCase, PAGE_SIZE, withMediaOnly)
            }
        )
    }

    private companion object {
        const val PAGE_SIZE = 20
    }
}
