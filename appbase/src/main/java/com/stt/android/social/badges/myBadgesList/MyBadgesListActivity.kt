package com.stt.android.social.badges.myBadgesList

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.social.badges.badgesDetail.BadgeDetailActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MyBadgesListActivity : ComponentActivity() {

    private val viewModel: MyBadgesListViewModel by viewModels()

    private fun handleMyBadgesListEvent(event: MyBadgesListViewEvent) {
        when (event) {
            is MyBadgesListViewEvent.Close -> {
                finish()
            }

            is MyBadgesListViewEvent.OnMyListBadgesClick -> startActivity(
                BadgeDetailActivity.newIntent(this, event.badgesId)
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            val viewData by viewModel.uiState.collectAsState()

            MyBadgesListScreen(
                viewData = viewData,
                onEvent = ::handleMyBadgesListEvent
            )
        }
    }

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, MyBadgesListActivity::class.java)
        }
    }
}
