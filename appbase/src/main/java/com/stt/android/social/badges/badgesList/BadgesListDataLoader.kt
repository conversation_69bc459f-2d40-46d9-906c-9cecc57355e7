package com.stt.android.social.badges.badgesList

import com.stt.android.social.badges.badgesUseCase.BadgesListUseCase
import javax.inject.Inject

internal class BadgesListDataLoader @Inject constructor(
    private val badgesUseCase: BadgesListUseCase
) {
    suspend fun loadBadgesList(moduleName: String): List<BadgesDisplayItem> {
        val allModules = badgesUseCase.getBadgeConfigs(moduleName)
        val userBadgeList = badgesUseCase.getUserBadgeList().userHasWonBadges ?: emptyList()
        val userBadgeMap = userBadgeList
            .flatMap { it.userBadges }
            .associateBy { it.badgeConfigId }
        val module = allModules.firstOrNull { it.moduleName == moduleName }
        return (module?.badgeConfigs?.map { badgeConfig ->
            val isAcquired = userBadgeMap.containsKey(badgeConfig.badgeConfigId)
            BadgesDisplayItem(
                isAcquired = isAcquired,
                moduleName = moduleName,
                badgeName = badgeConfig.badgeName,
                badgeConfigId = badgeConfig.badgeConfigId,
                badgeIconUrl = badgeConfig.badgeIconUrl,
                acquiredBadgeIconUrl = badgeConfig.acquiredBadgeIconUrl,
                badgeBackgroundImageUrl = badgeConfig.badgeBackgroundImageUrl,
                badgeType = badgeConfig.badgeType,
                acquisitionTime = userBadgeMap[badgeConfig.badgeConfigId]?.acquisitionTime,
            )
        } ?: emptyList())
            .sortedByDescending { it.isAcquired }
            .sortedByDescending { it.acquisitionTime }
    }
}
