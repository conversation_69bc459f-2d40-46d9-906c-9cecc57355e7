package com.stt.android.social.personalrecord

import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.data.EntityMapper
import javax.inject.Inject

class PersonalRecordLocalMapper @Inject constructor(
    moshi: <PERSON><PERSON>
) : EntityMapper<String, List<PersonalRecordResultItem>> {
    private val jsonAdapter = moshi.adapter<List<PersonalRecordResultItem>>(
        Types.newParameterizedType(
            List::class.java,
            PersonalRecordResultItem::class.java
        )
    )

    override fun toDataEntity(): (List<PersonalRecordResultItem>) -> String {
        return {
            jsonAdapter.toJson(it)
        }
    }

    override fun toDomainEntity(): (String) -> List<PersonalRecordResultItem> {
        return {
            if (it.isEmpty()) emptyList() else jsonAdapter.fromJson(it)?.toList() ?: emptyList()
        }
    }
}
