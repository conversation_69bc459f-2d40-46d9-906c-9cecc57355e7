package com.stt.android.social.personalrecord

import kotlinx.coroutines.delay
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

class PersonalRecordRemoteDataSource @Inject constructor(
    private val personalRecordRestApi: PersonalRecordRestApi
) : PersonalRecordDataSource {

    override suspend fun getPersonalRecord(firstDelayNeeded: Boolean): List<PersonalRecordResultItem> {
        return retryWithDelay(
            firstDelayNeeded = firstDelayNeeded,
            delayDuration = 5.seconds,
            shouldStopRetrying = { response -> response.metadata?.get(META_STATUS) != META_STATUS_CALCULATING }
        ) {
            personalRecordRestApi.getPersonalRecord()
        }.payloadOrThrow()
    }

    private suspend fun <T> retryWithDelay(
        firstDelayNeeded: Boolean,
        delayDuration: kotlin.time.Duration,
        maxRetries: Int = Int.MAX_VALUE,
        shouldStopRetrying: (T) -> Boolean = { false },
        block: suspend () -> T
    ): T {
        repeat(maxRetries) { attempt ->
            if (firstDelayNeeded || attempt != 0) {
                delay(delayDuration)
            }
            try {
                val result = block()
                if (shouldStopRetrying(result)) {
                    Timber.d("Retry successful on attempt $attempt")
                    return result
                } else {
                    Timber.d("Retry attempt $attempt: Condition not met, retrying...")
                }
            } catch (e: Exception) {
                Timber.w(
                    e,
                    "Retry attempt $attempt failed. Retrying in ${delayDuration.inWholeSeconds} seconds..."
                )
            }
        }
        throw Exception("Max retry attempts ($maxRetries) reached.")
    }

    companion object {
        const val META_STATUS = "status"
        const val META_STATUS_CALCULATING = "calculating"
    }
}
