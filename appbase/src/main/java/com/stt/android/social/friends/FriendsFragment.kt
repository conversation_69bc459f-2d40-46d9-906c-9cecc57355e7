package com.stt.android.social.friends

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.CallbackManager.Factory.create
import com.facebook.FacebookAuthorizationException
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.FacebookSdk.isInitialized
import com.facebook.login.LoginBehavior
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.domain.STTErrorCodes
import com.stt.android.exceptions.BackendException
import com.stt.android.extensions.openAppSettings
import com.stt.android.home.people.InviteFriendsHelper
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.session.SignInFlowHook
import com.stt.android.social.friends.facebook.FacebookFriendsActivity
import com.stt.android.social.userprofileV2.BaseUserProfileActivity
import com.stt.android.ui.utils.DialogHelper
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class FriendsFragment : Fragment() {
    private val viewModel: FriendsViewModel by activityViewModels()

    @Inject
    lateinit var inviteFriendsHelper: InviteFriendsHelper

    @Inject
    lateinit var signInFlowHook: SignInFlowHook

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    private var callbackManager: CallbackManager? = null
    private val loginResultFacebookCallback: FacebookCallback<LoginResult> =
        object : FacebookCallback<LoginResult> {
            override fun onSuccess(result: LoginResult) {
                viewModel.fbLoginSuccess(result.accessToken.token)
            }

            override fun onCancel() {
                Timber.d("User cancelled Facebook log in process")
                viewModel.setFacebookLoading(false)
            }

            override fun onError(error: FacebookException) {
                if (error is FacebookAuthorizationException) {
                    Timber.d(
                        "Current access token: %s",
                        AccessToken.getCurrentAccessToken()
                    )
                    if (AccessToken.getCurrentAccessToken() != null) {
                        LoginManager.getInstance().logOut()
                    }
                    loginWithFacebook()
                } else {
                    viewModel.fbLoginError(error)
                }
            }
        }

    private val requestReadContactsPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            inviteFriendsHelper.toPhoneContactActivity(requireContext())
        } else if (!shouldShowRequestPermissionRationale(Manifest.permission.READ_CONTACTS)) {
            requireContext().openAppSettings()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )
        setContentWithM3Theme {
            FriendsScreen(
                viewModel = viewModel,
                onFriendClick = ::onFriendClick,
                onPhoneContactsClick = ::onPhoneContactsClick,
                onFacebookFriendsClick = ::onFacebookFriendsClick,
                onInvitePeopleClick = ::onInvitePeopleClick,
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.friendsEventFlow.collect { event ->
                    when (event) {
                        is RequestLoginFacebook -> loginWithFacebook()
                        is FacebookLoginSuccess -> onFacebookLoggedIn()
                        is FacebookLoginError -> onFacebookLoginFailed(event.exception)
                        is FriendsError -> {}
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        callbackManager?.onActivityResult(requestCode, resultCode, data)
    }

    private fun onFriendClick(friend: Friend) {
        startActivity(
            BaseUserProfileActivity.newStartIntent(
                requireContext(),
                friend.username,
                false,
            )
        )
    }

    private fun onPhoneContactsClick() {
        if (EasyPermissions.hasPermissions(requireContext(), Manifest.permission.READ_CONTACTS)) {
            inviteFriendsHelper.toPhoneContactActivity(requireContext())
        } else {
            AlertDialog.Builder(requireContext())
                .setMessage(R.string.request_read_contact_rationale)
                .setPositiveButton(
                    R.string.ok
                ) { _, _ -> requestReadContactsPermissionLauncher.launch(Manifest.permission.READ_CONTACTS) }
                .setNegativeButton(R.string.cancel, null)
                .show()
        }
    }

    private fun onFacebookFriendsClick() {
        if (viewModel.isUserLoggedIn()) {
            viewModel.setFacebookLoading(true)
            if (!ANetworkProvider.isOnline()) {
                DialogHelper.showDialog(context, R.string.network_disabled_enable)
                // we need to check if facebook is linked in ST backend and if facebook token is valid
            } else if (viewModel.isFacebookReady()) {
                viewModel.checkFacebookTokenValid()
            } else {
                loginWithFacebook()
            }
        } else {
            // user is NOT logged in
            viewModel.setFacebookLoading(false)
            DialogHelper.showDialog(
                context,
                R.string.sign_up_to_follow_title,
                R.string.sign_up_to_follow_msg,
                { _, _ ->
                    requireActivity().startActivity(
                        signInFlowHook.newStartIntent(requireContext(), null)
                    )
                },
                { dialogInterface, _ -> dialogInterface.dismiss() }
            )
        }
    }

    private fun loginWithFacebook() {
        // We need to check if online in case this method is called on start.
        if (callbackManager == null) {
            callbackManager = create()
            LoginManager.getInstance().setLoginBehavior(LoginBehavior.NATIVE_WITH_FALLBACK)
            LoginManager.getInstance()
                .registerCallback(callbackManager, loginResultFacebookCallback)
        }
        LoginManager.getInstance()
            .logInWithReadPermissions(this, STTConstants.FB_READ_PERMISSION_LIST)
    }

    private fun onFacebookLoggedIn() {
        startActivity(
            FacebookFriendsActivity.newStartIntent(requireContext())
        )
    }

    private fun onFacebookLoginFailed(e: Throwable?) {
        if (e is BackendException && ((e.error == STTErrorCodes.FB_USER_ID_ALREADY_USED) || e.error == STTErrorCodes.FB_USER_TOKEN_ALREADY_IN_USE)) {
            if (isInitialized()) {
                LoginManager.getInstance().logOut()
            }
            DialogHelper.showDialog(context, R.string.error_549)
        } else {
            DialogHelper.showDialog(context, R.string.message_connect_facebook_failed)
        }
    }

    private fun onInvitePeopleClick() {
        viewModel.shareAppLink(requireActivity(), childFragmentManager)
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.social.friends.FriendsFragment.FRAGMENT_TAG"
        fun newInstance() = FriendsFragment()
    }
}
