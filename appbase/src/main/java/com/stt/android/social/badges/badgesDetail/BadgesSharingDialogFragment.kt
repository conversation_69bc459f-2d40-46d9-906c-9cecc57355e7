package com.stt.android.social.badges.badgesDetail

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.content.FileProvider
import androidx.core.content.res.ResourcesCompat
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.activityViewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.stt.android.R
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.sharing.AnalysisData
import com.stt.android.sharing.SharingHelper
import com.stt.android.sharing.SharingInfo
import com.stt.android.sharing.SharingType
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * App sharing dialog will show at the top of this dialog
 * If use the compose view [ModalBottomSheet], app sharing dialog will show at the bottom of this dialog when the screen rotate to landscape
 * so use the [BottomSheetDialogFragment] to avoid this issue
 */
@AndroidEntryPoint
class BadgesSharingDialogFragment : BottomSheetDialogFragment() {
    private val viewModel: BadgesDetailViewModel by activityViewModels()

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme).apply {
            behavior.skipCollapsed = true
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    @Inject
    lateinit var sharingHelper: SharingHelper
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Scrollable compose view can't draw all content to bitmap, because some view don't be rendered
        // so use the NestedScrollView to wrap the compose view
        return NestedScrollView(requireContext()).apply {
            background = ResourcesCompat.getDrawable(
                resources,
                R.drawable.bottom_sheet_share_background,
                null
            )
            val composeView = ComposeView(requireContext())
            addView(composeView)
            composeView.setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnLifecycleDestroyed(
                    viewLifecycleOwner
                )
            )
            composeView.setContentWithM3Theme {
                val viewData = viewModel.uiState.collectAsState().value
                if (viewData is BadgesDetailViewData.Loaded) {
                    BadgesSharingSheet(
                        badgesDetailLoad = viewData,
                        user = viewModel.user.collectAsState().value,
                        onScreenshot = { bitmap, sharing ->
                            val context = requireContext()
                            if (sharing) {
                                viewModel.saveBadgesImageToCache(
                                    context.externalCacheDir ?: context.cacheDir,
                                    bitmap
                                ) {
                                    val shareImageUri = FileProvider.getUriForFile(
                                        context,
                                        "${context.packageName}.FileProvider",
                                        it
                                    )
                                    val sharingInfo = SharingInfo(
                                        sharingType = SharingType.MEDIA,
                                        resourceUris = listOf(shareImageUri)
                                    )
                                    val shareByApp = sharingHelper.shareByApp(
                                        sharingInfo,
                                        childFragmentManager
                                    )
                                    if (!shareByApp) {
                                        sharingHelper.shareBySystem(
                                            sharingInfo,
                                            requireActivity(),
                                            // TODO implement analytics event
                                            AnalysisData(eventName = "", emptyMap())

                                        )
                                    }
                                }
                            } else {
                                viewModel.saveBadgesImageToMedia(
                                    context.contentResolver,
                                    bitmap
                                )
                            }
                        }
                    )
                }
            }
        }
    }
}
