package com.stt.android.social.workoutlistv2.usecase

import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.workout.WorkoutRepository
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.social.workoutlistv2.WorkoutPage
import javax.inject.Inject

class LoadWorkoutPageUseCase @Inject constructor(
    private val workoutRepository: WorkoutRepository,
    private val workoutCardLoader: WorkoutCardLoader,
    private val currentUserController: CurrentUserController,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
) {
    suspend operator fun invoke(username: String, page: Int, pageSize: Int, withMediaOnly: Boolean): WorkoutPage {
        val domainWorkouts = workoutRepository.fetchWorkoutsPage(username, page, pageSize, withMediaOnly)
        val isOwnWorkout = username == currentUserController.username
        val user = if (isOwnWorkout) {
            currentUserController.currentUser
        } else {
            getUserByUsernameUseCase.getUserByUsername(username, false)
        }
        val userWorkoutPairs = domainWorkouts.map {
            user to it
        }
        val workoutCardInfos = workoutCardLoader.buildWorkoutCardsByDomainWorkouts(
            userWorkoutPairs = userWorkoutPairs,
            isOwnWorkout = isOwnWorkout,
            includeCover = true,
        )
        return WorkoutPage(
            username = username,
            page = page,
            workouts = workoutCardInfos,
        )
    }
}
