package com.stt.android.social.badges.badgesDetail

import com.stt.android.data.badges.BadgeConditionName
import com.stt.android.data.badges.ExploreMore
import com.stt.android.data.badges.UserBadge

sealed interface BadgesDetailViewData {
    data object Initial : BadgesDetailViewData

    data class Loaded(
        val badgesDetail: UserBadge,
        val exploreMore: List<ExploreMore>,
        val conditionData: List<BadgeProgressValueWithTarget>,
        val badgesAchievementDataList: List<BadgesAchievementData>?,
        val progressValue: Float
    ) : BadgesDetailViewData

    data class Error(val error: Throwable) : BadgesDetailViewData
}

data class BadgeProgressValueWithTarget(
    val current: String,
    val target: String,
    val conditionName: BadgeConditionName?
)

data class BadgesAchievementData(
    val value: String,
    val unit: String,
    val explanation: String,
)

data class TargetAndCurrentValue(
    val currentValue: Int,
    val targetValue: Int,
    val conditionName: BadgeConditionName?
)
