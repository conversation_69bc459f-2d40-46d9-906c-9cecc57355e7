package com.stt.android.social.badges.badgesDetail

import android.annotation.SuppressLint
import android.content.Context
import com.stt.android.R
import com.stt.android.data.badges.BadgeAcquisitionCondition
import com.stt.android.data.badges.BadgeConditionName
import com.stt.android.data.badges.BadgeConditionType
import com.stt.android.data.badges.BadgesFields
import com.stt.android.data.badges.ExploreMore
import com.stt.android.data.badges.UserBadge
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.social.badges.badgesUseCase.BadgesDetailUseCase
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import com.stt.android.core.R as CR

class BadgeDetailDataLoader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val badgesDetailUseCase: BadgesDetailUseCase,
    private val infoModelFormatter: InfoModelFormatter,
) {
    suspend fun loadBadgesDetailData(badgeConfigId: String?): UserBadge {
        val userBadge: UserBadge = badgesDetailUseCase.getUserBadgeDetail(badgeConfigId)
        return userBadge
    }

    suspend fun loadExploreMoreData(badgeConfigId: String?): List<ExploreMore> {
        val exploreMore: List<ExploreMore>? =
            badgesDetailUseCase.getMoreBadgesInDetail(badgeConfigId)
        return exploreMore?.take(3) ?: emptyList()
    }

    suspend fun loadConditionsData(badgeConfigId: String?): List<BadgeProgressValueWithTarget> {
        val userBadge: UserBadge = badgesDetailUseCase.getUserBadgeDetail(badgeConfigId)
        val valueList = getTargetAndCurrentValue(userBadge)
        return getProgressString(valueList)
    }

    suspend fun getIndicatorValue(badgeConfigId: String?): Float {
        val userBadge: UserBadge = badgesDetailUseCase.getUserBadgeDetail(badgeConfigId)
        val valueList = getTargetAndCurrentValue(userBadge)
        return calculateProgressIndicatorValue(valueList)
    }

    suspend fun getAchievementListValue(badgeConfigId: String?): List<BadgesAchievementData>? {
        val userBadge: UserBadge = badgesDetailUseCase.getUserBadgeDetail(badgeConfigId)
        val achievementList = userBadge.transcriptDataFields?.mapNotNull { field ->
            when (field) {
                BadgesFields.TOTAL_ACTIVITIES -> userBadge.activitySessions?.let { value ->
                    BadgesAchievementData(
                        value = value.toString(),
                        unit = "",
                        explanation = context.getString(R.string.total_activities_badges)
                    )
                }

                BadgesFields.TOTAL_CALORIES -> userBadge.energy?.let { value ->
                    val valueString = value.toString()
                    val unitString = context.getString(CR.string.kcal)
                    BadgesAchievementData(
                        value = valueString,
                        unit = unitString,
                        explanation = context.getString(R.string.energy)
                    )
                }

                BadgesFields.TOTAL_WORKOUT_DAYS -> userBadge.totalWorkoutDays?.let { value ->
                    BadgesAchievementData(
                        value = value.toString(),
                        unit = "",
                        explanation = context.getString(R.string.total_workoutDays_badges)
                    )
                }

                BadgesFields.TOTAL_ASCENT -> userBadge.totalAscent?.let { value ->
                    val ascentResult =
                        infoModelFormatter.formatValue(SummaryItem.ASCENTALTITUDE, value)
                    BadgesAchievementData(
                        value = ascentResult.value.toString(),
                        unit = ascentResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.total_ascent_badges)
                    )
                }

                BadgesFields.TOTAL_WORKOUT_DURATION -> userBadge.totalDuration?.let { value ->
                    val minuteTotal = value / (100.0 * 60)
                    val hourValue = (minuteTotal / 60)
                    val minuteValue = (minuteTotal % 60)
                    val valueString = if (hourValue > 0) {
                        hourValue.toInt().toString()
                    } else {
                        minuteValue.toInt().toString()
                    }
                    val unitId = if (hourValue > 0) {
                        CR.string.hour
                    } else {
                        CR.string.minute
                    }
                    BadgesAchievementData(
                        value = valueString,
                        unit = context.getString(unitId),
                        explanation = context.getString(R.string.total_duration_badges)
                    )
                }

                BadgesFields.MAX_PACE -> userBadge.maxPace?.let { value ->
                    val paceValue = value
                    val paceResult = infoModelFormatter.formatValue(SummaryItem.MAXPACE, paceValue)
                    BadgesAchievementData(
                        value = paceResult.value.toString(),
                        unit = paceResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.max_pace_badges)
                    )
                }

                BadgesFields.MAX_DISTANCE -> userBadge.maxDistance?.let { value ->
                    val distanceValue = value / (100.0)
                    val distanceResult =
                        infoModelFormatter.formatValue(SummaryItem.DISTANCE, distanceValue)
                    BadgesAchievementData(
                        value = distanceResult.value.toString(),
                        unit = distanceResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.max_distance_badges)
                    )
                }

                BadgesFields.MAX_CYCLING_SPEED -> userBadge.maxCyclingSpeed?.let { value ->
                    val speedValue = value.toDouble()
                    val distanceResult =
                        infoModelFormatter.formatValue(SummaryItem.MAXSPEED, speedValue.toDouble())
                    BadgesAchievementData(
                        value = distanceResult.value.toString(),
                        unit = distanceResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.max_cycling_speed_badges)
                    )
                }

                BadgesFields.MAX_DIVING_DEPTH -> userBadge.maxDivingDepth?.let { value ->
                    val depthValue = value / 100.0
                    val depthResult =
                        infoModelFormatter.formatValue(SummaryItem.MAXDEPTH, depthValue)
                    BadgesAchievementData(
                        value = depthResult.value.toString(),
                        unit = depthResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.max_diving_depth_badges)
                    )
                }

                BadgesFields.MAX_SPEED -> userBadge.maxSpeed?.let { value ->
                    val speedValue = value.toDouble()
                    val distanceResult =
                        infoModelFormatter.formatValue(SummaryItem.MAXSPEED, speedValue)
                    BadgesAchievementData(
                        value = distanceResult.value.toString(),
                        unit = distanceResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.max_speed_badges)
                    )
                }

                BadgesFields.MAX_DURATION -> userBadge.maxDuration?.let { value ->
                    val seconds = value / (100.0)
                    val durationResult =
                        infoModelFormatter.formatValue(SummaryItem.DURATION, seconds)
                    BadgesAchievementData(
                        value = durationResult.value.toString(),
                        unit = durationResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.max_duration_badges)
                    )
                }

                BadgesFields.TOTAL_DISTANCE -> userBadge.totalDistance?.let { value ->
                    val distanceValue = value / (100.0)
                    val distanceResult =
                        infoModelFormatter.formatValue(SummaryItem.DISTANCE, distanceValue)
                    BadgesAchievementData(
                        value = distanceResult.value.toString(),
                        unit = distanceResult.unit?.let { context.getString(it) } ?: "",
                        explanation = context.getString(R.string.total_distance_badges)
                    )
                }
            }
        }
        return achievementList
    }

    private fun getTargetAndCurrentValue(userBadge: UserBadge): List<TargetAndCurrentValue> {
        val leafConditions = extractLeafConditions(userBadge.acquisitionCondition)
        return leafConditions.map { condition ->
            val value = getCurrentIntValue(userBadge, condition)
            TargetAndCurrentValue(value.first, value.second, condition.conditionName)
        }
    }

    private fun extractLeafConditions(condition: BadgeAcquisitionCondition?): List<BadgeAcquisitionCondition> {
        if (condition == null) return emptyList()
        if (condition.type == BadgeConditionType.LEAF) return listOf(condition)
        return condition.childConditions?.flatMap { extractLeafConditions(it) } ?: emptyList()
    }

    @SuppressLint("DefaultLocale")
    private fun getProgressString(value: List<TargetAndCurrentValue>): List<BadgeProgressValueWithTarget> {
        return value.map { conditionValue ->
            when (val conditionName = conditionValue.conditionName) {
                BadgeConditionName.DURATION -> {
                    val currentHoursValue = conditionValue.currentValue / 60
                    val currentMinuteValue = conditionValue.currentValue % 60
                    val targetHoursValue = conditionValue.targetValue / 60
                    val targetMinuteValue = conditionValue.targetValue % 60
                    val current = if (currentHoursValue > 0) {
                        currentHoursValue.toString() + " " + context.getString(CR.string.hour)
                    } else {
                        currentMinuteValue.toString() + " " + context.getString(CR.string.minute)
                    }
                    val target = if (targetHoursValue > 0) {
                        targetHoursValue.toString() + " " + context.getString(CR.string.hour)
                    } else {
                        targetMinuteValue.toString() + " " + context.getString(CR.string.minute)
                    }
                    BadgeProgressValueWithTarget(current, target, conditionName)
                }

                BadgeConditionName.DISTANCE -> {
                    val current = infoModelFormatter.formatValueAsString(
                        SummaryItem.DISTANCE,
                        conditionValue.currentValue
                    )
                    val target = infoModelFormatter.formatValueAsString(
                        SummaryItem.DISTANCE,
                        conditionValue.targetValue
                    )
                    BadgeProgressValueWithTarget(current, target, conditionName)
                }

                BadgeConditionName.ASCENT -> {
                    val current = infoModelFormatter.formatValueAsString(
                        SummaryItem.ASCENTALTITUDE,
                        conditionValue.currentValue
                    )
                    val target = infoModelFormatter.formatValueAsString(
                        SummaryItem.ASCENTALTITUDE,
                        conditionValue.targetValue
                    )
                    BadgeProgressValueWithTarget(current, target, conditionName)
                }

                BadgeConditionName.ENERGY -> {
                    val current = infoModelFormatter.formatValueAsString(
                        SummaryItem.ENERGY,
                        conditionValue.currentValue
                    )
                    val target = infoModelFormatter.formatValueAsString(
                        SummaryItem.ENERGY,
                        conditionValue.targetValue
                    )
                    BadgeProgressValueWithTarget(current, target, conditionName)
                }

                BadgeConditionName.WORKOUT_NUMBER -> {
                    val current = conditionValue.currentValue.toString()
                    val target =
                        conditionValue.targetValue.toString() + " " + context.getString(R.string.activity_times)
                            .replaceFirstChar { it.lowercase() }
                    BadgeProgressValueWithTarget(current, target, conditionName)
                }

                BadgeConditionName.TOTAL_UTMB_DISTANCE -> {
                    val current = infoModelFormatter.formatValueAsString(
                        SummaryItem.DISTANCE,
                        conditionValue.currentValue
                    )
                    val target = infoModelFormatter.formatValueAsString(
                        SummaryItem.DISTANCE,
                        conditionValue.targetValue
                    )
                    BadgeProgressValueWithTarget(current, target, conditionName)
                }

                null -> BadgeProgressValueWithTarget("null", "null", conditionName)
            }
        }
    }

    @SuppressLint("DefaultLocale")
    private fun getCurrentIntValue(
        userBadge: UserBadge,
        acquisitionCondition: BadgeAcquisitionCondition
    ): Pair<Int, Int> {
        val conditionName = acquisitionCondition.conditionName
        return when (conditionName) {
            // unit is " minute "
            BadgeConditionName.DURATION -> {
                val current = userBadge.totalDuration?.let { duration ->
                    millisecondsToMinute(duration)
                } ?: 0
                val target =
                    millisecondsToMinute((acquisitionCondition.targetVal?.toDouble() ?: 0.0))
                return Pair(current, target)
            }

            // unit is " m "
            BadgeConditionName.DISTANCE -> {
                val current = userBadge.totalDistance?.let { distance ->
                    distance.toInt() / 100
                } ?: 0
                val target = (acquisitionCondition.targetVal?.toInt()?.div(100)) ?: 0
                return Pair(current, target)
            }

            // unit is " m "
            BadgeConditionName.ASCENT -> {
                val current = userBadge.totalAscent?.let { ascent ->
                    ascent / 100
                } ?: 0
                val target = (acquisitionCondition.targetVal?.toInt()?.div(100)) ?: 0
                return Pair(current, target)
            }

            // unit is " kcal "
            BadgeConditionName.ENERGY -> {
                val current = userBadge.energy ?: 0
                val target = (acquisitionCondition.targetVal?.toInt()) ?: 0
                return Pair(current, target)
            }

            // unit is " activities "
            BadgeConditionName.WORKOUT_NUMBER -> {
                val current = userBadge.activitySessions ?: 0
                val target = (acquisitionCondition.targetVal?.toInt()) ?: 0
                return Pair(current, target)
            }

            // unit is " m "
            BadgeConditionName.TOTAL_UTMB_DISTANCE -> {
                val current = userBadge.totalUTMBDistance?.toInt() ?: 0
                val target = acquisitionCondition.targetVal?.toInt() ?: 0
                return Pair(current, target)
            }

            else -> Pair(0, 0)
        }
    }

    private fun calculateProgressIndicatorValue(value: List<TargetAndCurrentValue>): Float {
        if (value.isEmpty()) return 0f

        val averageRatio = value
            .map { it.currentValue.toFloat() / it.targetValue.toFloat() }
            .average()
            .toFloat()

        return averageRatio.coerceIn(0f, 1f)
    }

    private fun millisecondsToMinute(milliseconds: Double): Int {
        return (milliseconds / 6000.0).toInt()
    }
}
