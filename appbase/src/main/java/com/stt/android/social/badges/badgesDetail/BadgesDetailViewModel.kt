package com.stt.android.social.badges.badgesDetail

import android.content.ContentResolver
import android.graphics.Bitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.multimedia.MediaStoreUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class BadgesDetailViewModel @Inject constructor(
    private val dataLoader: BadgeDetailDataLoader,
    currentUserController: CurrentUserController,
) : ViewModel() {
    private val _uiState = MutableStateFlow<BadgesDetailViewData>(BadgesDetailViewData.Initial)
    val uiState: StateFlow<BadgesDetailViewData> = _uiState.asStateFlow()

    private val _user = MutableStateFlow(currentUserController.currentUser)
    val user = _user.asStateFlow()

    fun loadBadgeDetail(badgeConfigId: String?) {
        viewModelScope.launch {
            runSuspendCatching {
                val userBadge = dataLoader.loadBadgesDetailData(badgeConfigId)
                val exploreMore = dataLoader.loadExploreMoreData(badgeConfigId)
                val conditions = dataLoader.loadConditionsData(badgeConfigId)
                val progressValue = dataLoader.getIndicatorValue(badgeConfigId)
                val achievementList = dataLoader.getAchievementListValue(badgeConfigId)

                _uiState.value = BadgesDetailViewData.Loaded(
                    badgesDetail = userBadge,
                    exploreMore = exploreMore,
                    conditionData = conditions,
                    badgesAchievementDataList = achievementList,
                    progressValue = progressValue,
                )
            }.onFailure {
                Timber.w(it, "Fail to load badges.")
                _uiState.value = BadgesDetailViewData.Error(it)
            }
        }
    }

    fun saveBadgesImageToMedia(contentResolver: ContentResolver, bitmap: Bitmap) {
        viewModelScope.launch {
            runSuspendCatching {
                val displayName = UUID.randomUUID().toString().take(8).plus(".jpg")
                MediaStoreUtils.saveMediaToMediaStore(
                    contentResolver,
                    displayName
                ) { fileDescriptor ->
                    FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
                    }
                }
            }.onFailure {
                Timber.w(it, "Fail to save sharing image.")
            }
        }
    }

    fun saveBadgesImageToCache(cacheDir: File, image: Bitmap, onSavedSuccessfully: (File) -> Unit) {
        viewModelScope.launch {
            runSuspendCatching {
                val shareImageFileCacheDir = File(
                    cacheDir,
                    "badge_share"
                ).apply { mkdirs() }
                val imageFile = File(
                    shareImageFileCacheDir,
                    UUID.randomUUID().toString().plus(".jpg")
                )
                FileOutputStream(imageFile).use {
                    image.compress(Bitmap.CompressFormat.JPEG, 80, it)
                }
                imageFile
            }.onSuccess {
                onSavedSuccessfully(it)
            }.onFailure {
                Timber.w(it, "Fail to save badges image.")
            }
        }
    }
}
