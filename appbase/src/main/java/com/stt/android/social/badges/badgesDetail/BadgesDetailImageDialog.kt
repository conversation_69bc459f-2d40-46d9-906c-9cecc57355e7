package com.stt.android.social.badges.badgesDetail

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.DialogWindowProvider
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

private const val ANIMATION_DURATION_MS = 300

@Composable
fun BadgesDetailImageDialog(
    showDialog: Boolean,
    onDismissRequest: () -> Unit,
    disableScrim: Boolean = false,
    useSystemBarTheme: Boolean = true,
    content: @Composable (onDismiss: () -> Unit) -> Unit,
) {
    var animationVisible by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()
    val systemUiController = rememberSystemUiController()

    if (showDialog) {
        Dialog(
            onDismissRequest = {
                scope.launch {
                    animationVisible = false
                    delay(ANIMATION_DURATION_MS.toLong())
                    onDismissRequest()
                }
            },
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                decorFitsSystemWindows = useSystemBarTheme
            )
        ) {
            // If disableScrim is true, we need to disable the scrim effect
            if (disableScrim) {
                val view = LocalView.current
                DisposableEffect(view) {
                    val parent = view.parent
                    if (parent is DialogWindowProvider) {
                        // Set the dialog window background to transparent to disable the scrim
                        parent.window.setDimAmount(0f)
                    }
                    onDispose { }
                }
            }
            // This Box is crucial to disable a growing animation by the platform
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .navigationBarsPadding()
                    .statusBarsPadding()
            ) {
                LaunchedEffect(Unit) {
                    animationVisible = true
                }

                // If useSystemBarTheme is false, we explicitly set the system bar colors
                // to prevent the dialog from changing them to dark theme
                if (!useSystemBarTheme) {
                    DisposableEffect(Unit) {
                        // Set light theme for system bars
                        systemUiController.setSystemBarsColor(
                            color = Color.Transparent,
                            darkIcons = true
                        )
                        onDispose { }
                    }
                }

                AnimatedVisibility(
                    visible = animationVisible,
                    enter = androidx.compose.animation.EnterTransition.None,
                    exit = androidx.compose.animation.ExitTransition.None,
                ) {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = Color.Transparent,
                    ) {
                        content {
                            scope.launch {
                                animationVisible = false
                                delay(ANIMATION_DURATION_MS.toLong())
                                onDismissRequest()
                            }
                        }
                    }
                }
            }
        }
    }
}
