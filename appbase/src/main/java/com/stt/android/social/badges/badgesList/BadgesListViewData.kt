package com.stt.android.social.badges.badgesList

import com.stt.android.data.badges.BadgeType

internal sealed interface BadgesListViewData {
    data object Initial : BadgesListViewData

    data class Loaded(
        val moduleName: String,
        val badgesList: List<BadgesDisplayItem>,
        val topActivityBadges: Map<String, List<BadgesDisplayItem>>? = null,
    ) : BadgesListViewData
}

data class BadgesDisplayItem(
    val isAcquired: Boolean,
    val moduleName: String,
    val badgeName: String?,
    val badgeConfigId: String,
    val badgeIconUrl: String?,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?,
    val badgeType: BadgeType?,
    val acquisitionTime: Long? = null,
)
