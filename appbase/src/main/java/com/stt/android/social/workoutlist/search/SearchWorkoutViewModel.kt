package com.stt.android.social.workoutlist.search

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.loadRemoteWorkouts
import com.stt.android.controllers.loadWorkouts
import com.stt.android.controllers.workoutUpdated
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.BackendWorkout
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.follow.IsFolloweeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.social.workoutlist.DateHeader
import com.stt.android.social.workoutlist.search.SearchWorkoutActivity.Companion.KEY_USERNAME
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
@HiltViewModel
class SearchWorkoutViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    savedStateHandle: SavedStateHandle,
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutCardLoader: WorkoutCardLoader,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val isFolloweeUseCase: IsFolloweeUseCase,
    private val eventTracker: EventTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    val workoutCardActionsHandler: WorkoutCardActionsHandler,
) : ViewModel() {

    private val _searchQuery = MutableSharedFlow<String>(extraBufferCapacity = 1)

    private val _viewState = MutableStateFlow(
        SearchWorkoutViewState("", emptyList(), false)
    )
    val viewState: StateFlow<SearchWorkoutViewState> = _viewState.asStateFlow()

    private val username =
        savedStateHandle.get<String>(KEY_USERNAME) ?: currentUserController.username
    private val trackPageName = savedStateHandle.get<String>(AnalyticsEventProperty.PAGE_NAME) ?: ""

    private val isCurrentUser: Boolean
        get() = username == currentUserController.username

    private val _workoutUpdateFlow = workoutHeaderController
        .workoutUpdated
        .onStart { emit(Unit) }

    init {
        _searchQuery
            .debounce(DELAY_FOR_SEARCH)
            .distinctUntilChanged()
            .flatMapLatest { query ->
                flow {
                    if (query.trim().isEmpty()) {
                        emit(
                            _viewState.value.copy(
                                searching = false,
                                dateAndWorkouts = emptyList(),
                            )
                        )
                    } else {
                        emit(
                            _viewState.value.copy(
                                searching = true,
                            )
                        )
                        emit(updatedViewState(query))
                    }
                }
            }
            .onEach { state ->
                _viewState.update { state }
            }
            .launchIn(viewModelScope)

        _workoutUpdateFlow
            .onEach {
                val keyword = _viewState.value.keyword
                val searched = _viewState.value.dateAndWorkouts.any()
                if (searched) {
                    val state = updatedViewState(keyword)
                    _viewState.update { state }
                }
            }
            .launchIn(viewModelScope)
    }

    private suspend fun updatedViewState(query: String): SearchWorkoutViewState =
        withContext(coroutinesDispatchers.io) {
            if (query.isNotBlank()) {
                eventTracker.trackEvent(
                    AnalyticsEvent.SEARCH_REQUEST,
                    mapOf(
                        AnalyticsEventProperty.PAGE_NAME to trackPageName,
                        AnalyticsEventProperty.SEARCH_WORD to query,
                    )
                )
            }
            val filteredWorkouts = getAllWorkouts()
                .takeUnless { it.isEmpty() }
                ?.filter {
                    if (query.isBlank()) {
                        false
                    } else {
                        val searchTerms = query.lowercase().split(" ").toTypedArray()
                        it.second.applyFilter(searchTerms, context.resources)
                    }
                }
                ?.sortedByDescending {
                    it.second.startTime
                }
                ?: emptyList()

            val user = if (isCurrentUser) {
                currentUserController.currentUser
            } else {
                runSuspendCatching {
                    getUserByUsernameUseCase.getUserByUsername(username, false)
                }.getOrElse {
                    getUserByUsernameUseCase.getUserByUsername(username, true)
                }
            }
            val storeLocal = isCurrentUser || isFolloweeUseCase.isFollowee(username)
            val filteredWorkoutCards = if (storeLocal) {
                workoutCardLoader.buildWorkoutCards(
                    userWorkoutPairs = filteredWorkouts.map { user to it.second },
                    isOwnWorkout = isCurrentUser,
                    includeCover = true,
                )
            } else {
                workoutCardLoader.buildRemoteWorkoutCards(
                    userWorkoutTriples = filteredWorkouts.mapNotNull {
                        it.first?.let { backendWorkout ->
                            Triple(
                                user,
                                it.second,
                                backendWorkout,
                            )
                        }
                    },
                    includeCover = true,
                )
            }

            val filteredWorkoutsWithDates = filteredWorkoutCards.groupBy {
                TextFormatter.formatYearMonth(context, it.workoutHeader.startTime)
            }.flatMap { (date, workouts) ->
                listOf(DateHeader(date, workouts.size)) + workouts
            }

            _viewState.value.copy(
                searching = false,
                dateAndWorkouts = filteredWorkoutsWithDates,
            )
        }

    private suspend fun getAllWorkouts(): List<Pair<BackendWorkout?, WorkoutHeader>> =
        withContext(coroutinesDispatchers.io) {
            runSuspendCatching {
                if (isCurrentUser || isFolloweeUseCase.isFollowee(username)) {
                    workoutHeaderController.loadWorkouts(username).last().map {
                        null to it
                    }
                } else {
                    workoutHeaderController.loadRemoteWorkouts(username).first()
                }
            }.onFailure {
                Timber.w(it, "Failed to load workouts")
            }.getOrElse { emptyList() }
        }

    fun onQueryChange(query: String) {
        if (_viewState.value.keyword == query) return
        _viewState.update {
            it.copy(
                keyword = query,
                searching = query.trim().isNotBlank(),
            )
        }
        _searchQuery.tryEmit(query)
    }

    private companion object {
        private const val DELAY_FOR_SEARCH = 500L
    }
}
