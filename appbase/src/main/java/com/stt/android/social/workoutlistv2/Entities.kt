package com.stt.android.social.workoutlistv2

import android.net.Uri
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.newfeed.WorkoutCardInfo

data class WorkoutPage(
    val username: String,
    val page: Int,
    val workouts: List<WorkoutCardInfo>,
)

sealed class WorkoutCardItem {
    data class Header(val yearMonth: String) : WorkoutCardItem()
    data class Workout(val data: WorkoutCardInfo) : WorkoutCardItem()
}

sealed class WorkoutMediaItem {
    abstract val uri: Uri
    abstract val workoutHeader: WorkoutHeader
    data class Photo(
        override val uri: Uri,
        override val workoutHeader: WorkoutHeader
    ) : WorkoutMediaItem()

    data class Video(
        override val uri: Uri,
        override val workoutHeader: WorkoutHeader,
        val userAgent: String
    ) : WorkoutMediaItem()
}
