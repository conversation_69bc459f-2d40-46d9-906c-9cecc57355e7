package com.stt.android.social.personalrecord

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class RemoteRecordItem(
    val date: Long,
    val type: String,
    val value: Double,
    val workoutKey: String
)

@JsonClass(generateAdapter = true)
data class PersonalRecordResultItem(
    val activityId: Int,
    val records: Records,
    val types: List<String>
)

@JsonClass(generateAdapter = true)
data class Records(
    @<PERSON><PERSON>(name = "FiveKilometers") val fiveKilometers: RemoteRecordItem?,
    @<PERSON><PERSON>(name = "TenKilometers") val tenKilometers: RemoteRecordItem?,
    @<PERSON><PERSON>(name = "HalfMarathon") val halfMarathon: RemoteRecordItem?,
    @<PERSON><PERSON>(name = "FullMarathon") val fullMarathon: RemoteRecordItem?,
    @<PERSON><PERSON>(name = "LongestDistance") val longestDistance: RemoteRecordItem?,
    @<PERSON><PERSON>(name = "FastestPace") val fastestPace: RemoteRecordItem?,
    @Json(name = "HighestAltitude") val highestAltitude: RemoteRecordItem?,
    @<PERSON><PERSON>(name = "HighestClimb") val highestClimb: RemoteRecordItem?,
    @Json(name = "HighestSpeed") val highestSpeed: RemoteRecordItem?
) {
    fun isAllEmpty(): Boolean {
        return fiveKilometers == null &&
            tenKilometers == null &&
            halfMarathon == null &&
            fullMarathon == null &&
            longestDistance == null &&
            fastestPace == null &&
            highestAltitude == null &&
            highestClimb == null &&
            highestSpeed == null
    }
}
