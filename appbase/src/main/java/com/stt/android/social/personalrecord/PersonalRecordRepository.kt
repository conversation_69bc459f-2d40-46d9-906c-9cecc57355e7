package com.stt.android.social.personalrecord

import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.findOrFetch
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import timber.log.Timber
import javax.inject.Inject

class PersonalRecordRepository @Inject constructor(
    private val personalRecordLocalDataSource: PersonalRecordLocalDataSource,
    private val personalRecordRemoteDataSource: PersonalRecordRemoteDataSource,
    private val workoutHeaderController: WorkoutHeaderController,
) {
    suspend fun loadWorkoutHeader(workoutKey: String): WorkoutHeader? = runSuspendCatching {
        workoutHeaderController.findOrFetch(workoutKey)
    }.getOrElse { e ->
        Timber.w(e, "An error has occurred while trying to load workoutHeader by key")
        null
    }

    suspend fun fetchPersonalRecordFromDataSource(): List<PersonalRecordResultItem> {
        val localRecords = personalRecordLocalDataSource.getPersonalRecord()
        return if (personalRecordNeedsUpdated(localRecords)) {
            fetchRemotePersonalRecordAndStoreLocal(true)
        } else {
            localRecords
        }
    }

    private suspend fun personalRecordNeedsUpdated(localRecords: List<PersonalRecordResultItem>): Boolean =
        personalRecordLocalDataSource.currentUserWorkoutUpdated() || localRecords.isEmpty()

    suspend fun fetchRemotePersonalRecordAndStoreLocal(firstDelayNeeded: Boolean): List<PersonalRecordResultItem> {
        val personalRecords = runSuspendCatching {
            personalRecordRemoteDataSource.getPersonalRecord(firstDelayNeeded)
        }.getOrElse {
            emptyList()
        }
        if (personalRecords.isNotEmpty() && personalRecords.any { !it.records.isAllEmpty() }) {
            personalRecordLocalDataSource.savePersonalRecord(personalRecords)
        }
        return personalRecords
    }
}
