#### 6.1.0 (2025-08-12)
 - Fixes
  - 191204 191206 update strings by author:Jiang<PERSON><PERSON>ming<PERSON>
  - The bottom part of the button layout was hidden when tapping the next point in the tip view by author:JessieeLi
  - 191501 191293 191349 191458 191472 190417 191448 191416 191473 191479 some issues for climb guidance 2 by author:JessieeLi
  - 191426 191247 191210 191150 191258 191155 191244 190551 Fix multisport mode customization bugs by author:wang<PERSON><PERSON>un-suunto
  - 191538 191545 bugs of badges detail and dialog by author:liangkun3892
  - 191007 Editing waypoint doesn't work by author:<PERSON><PERSON><PERSON>i
  - Some tp bugs and badges ui bugs by author:liangkun3892
  - 190922,190463,190325,189896 chart detail bug by author:shua<PERSON><PERSON>chen
  - 190712 190947 191004 191026 191033 191037 191040 191047 191049 191140 191152 fix bugs of badges by author:liangkun3892
  - Fix crash in vo2max widget by author:wang<PERSON>ochun-suunto
  - Handle sport mode component compability by author:wang<PERSON><PERSON><PERSON>-suunto
  - 191270 191281 191403 191400 191406 by author:<PERSON>
  - 190833,190837,190847,190849,190950 VO2Max chart bug by author:shuaiwenchen
  - 191179 191196 191061 191149 AI planner  crash, text changes, minor ui fixes by author:Xizhi Zhu (Steven)
  - 191300 Fix firebase crashes in widget charts & sport mode header by author:wangxiaochun-suunto
  - 191134 191131 190846 Fix badges bugs 3/x by author:Yazhou66
  - 188394 Hide fast cycling map for China by author:JiangXianming2
  - Add string for duration widget name by author:JiangXianming2
  - UI adjustments for climb guidance 2 by author:JessieeLi
  - Should load all pictures of the user by author:Xizhi Zhu (Steven)
  - 190084 189918 update strings by author:JessieeLi

 - Features
  - 191559 Day view v2 enhancements by author:JiangXianming2
  - 191520 Day view v2, 9/9 by author:dt2dev
  - 179171 179169 191481 Update SU09 resource by author:Yazhou66
  - 191238 Day view v2, 8/x by author:dt2dev
  - 189433 Optimize User Profile page phase 2, 3/x by author:wangxiaochun-suunto
  - 191092 Day view v2, 7/x by author:dt2dev
  - remove toggle for climb guidance 2 [dev] by author:Xizhi Zhu (Steven)
  - 187459 Show planned workouts in day view and sharing view by author:Sami Rajala
  - 187306 Support offline maps on mobile, 7/x by author:Xizhi Zhu (Steven)
  - 191092 Day view v2, 6/x by author:dt2dev
  - 187306 Support offline maps on mobile, 6/x by author:Xizhi Zhu (Steven)
  - 190791 Day view v2, 5/x by author:dt2dev
  - 187306 Support offline maps on mobile, 5/x by author:Xizhi Zhu (Steven)
  - 186685 Support multisport mode customization in NG3 watch, 6/x by author:wangxiaochun-suunto

 - Technical
  - Enable some lint rules by author:JiangXianming2
  - Add date wheel picker compose ui by author:moon
  - Exclude kotlin-compiler-embeddable by author:JiangXianming2
  - Update ktlint config by author:JiangXianming2
  - Update target SDK to 35 by author:JiangXianming2
  - Remove dead code related to MC networking by author:Xizhi Zhu (Steven)
  - Remove RxJava1 from various places by author:Xizhi Zhu (Steven)
  - Update SDS to 3.32.8 & Bump dependencies by author:Xizhi Zhu (Steven)
  - Remove memoQ by author:JiangXianming2
  - Remove RxJava from WorkoutCommentController by author:Xizhi Zhu (Steven)
  - Remove Rx from PictureControllers by author:Xizhi Zhu (Steven)
  - Bump dependencies by author:JiangXianming2
  - Fix database version by author:JiangXianming2
